#include <stdlib.h>
#include "logic.h"
#include "types.h"
#include <stdio.h>
#include <stdbool.h>

int roll_die() {
    return (rand() % 6) + 1; // 1-6
}



// return 1 if the given color has all pieces in home
int check_win(Color c) {
    for(int i = 0; i <PIECES_PER_PLAYER; i++) {
        if (!all_pieces[c][i].is_in_home) return 0;
    }
    return 1;
}

void print_outcome(Outcomes outcome, Player* player, int source_pos, int dest_pos, Piece* victim, Cell* dest_cell) {
    Color color = player->color;
    const char* color_name = get_color_name(color);
    
    switch (outcome) {
        case FR_BASE:
            printf("[%s] player moves piece %s%d to the starting point.\n", 
                   color_name, 
                   get_color_name(color)[0], 
                   player->last_move->id);
            printf("[%s] player now has %d/4 pieces on the board and %d/4 pieces on the base.\n",
                   color_name, 
                   player->in_board, 
                   player->in_base);
            break;
            
        case ST_PATH:
            printf("[%s] moves piece %c%d from location %d to %d by %d units in clockwise direction.\n",
                   color_name,
                   get_color_name(color)[0],
                   player->last_move->id,
                   source_pos,
                   dest_pos,
                   dest_pos - source_pos > 0 ? dest_pos - source_pos : dest_pos - source_pos + STD_BOARD_SIZE);
            break;
            
        case BLOCKED:
            printf("[%s] piece %c%d is blocked from moving from %d to %d by [%s] piece %c%d.\n",
                   color_name,
                   get_color_name(color)[0],
                   player->last_move->id,
                   source_pos,
                   dest_pos,
                   get_color_name(dest_cell->occupants[0]->color),
                   get_color_name(dest_cell->occupants[0]->color)[0],
                   dest_cell->occupants[0]->id);
            
            if (player->in_board <= 1) {
                printf("[%s] does not have other pieces in the board to move instead of the blocked piece.\n",
                       color_name);
                printf("Ignoring the throw and moving on to the next player.\n");
            } else {
                printf("[%s] does not have other pieces in the board to move instead of the blocked piece.\n",
                       color_name);
                printf("Moved the piece to square %d which is the cell before the block.\n", 
                       source_pos);
            }
            break;
            
        case CAPTURE:
            if (victim && victim->last_victim) {
                printf("[%s] piece %c%d lands on square %d, captures [%s] piece %c%d, and returns it to the base.\n",
                       color_name,
                       get_color_name(color)[0],
                       player->last_move->id,
                       dest_pos,
                       get_color_name(victim->last_victim->color),
                       get_color_name(victim->last_victim->color)[0],
                       victim->last_victim->id);
                
                printf("[%s] player now has %d/4 pieces on the board and %d/4 pieces on the base.\n",
                       get_color_name(victim->last_victim->color),
                       victim->in_board,
                       victim->in_base);
            }
            break;
            
        case H_PATH:
            printf("[%s] piece %c%d enters the home path at position %d.\n",
                   color_name,
                   get_color_name(color)[0],
                   player->last_move->id,
                   dest_pos);
            break;
            
        case HOME:
            printf("[%s] piece %c%d reaches home!\n",
                   color_name,
                   get_color_name(color)[0],
                   player->last_move->id);
            printf("[%s] player now has %d/4 pieces at home.\n",
                   color_name,
                   player->in_home);
            break;
            
        case NON:
            printf("[%s] cannot move this turn.\n", color_name);
            break;
    }
    
    // After each round status
    printf("\n============================\n");
    printf("Location of pieces [%s]\n", color_name);
    printf("============================\n");
    
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece* p = &all_pieces[color][i];
        printf("Piece %c%d -> ", get_color_name(color)[0], p->id);
        
        if (p->is_in_base) {
            printf("Base\n");
        } else if (p->is_in_home) {
            printf("Home\n");
        } else if (p->is_in_home_path) {
            printf("Home path position %d\n", p->home_index);
        } else {
            printf("Board position %d\n", p->board_pos);
        }
    }
}

// KEY BUG FIXES for the Ludo game logic

// 1. Fix in can_move_piece() - Array bounds violation
int can_move_piece(Piece *p, int die_roll, int* target_pos, int* is_capture, int* path_blocked) {
    *path_blocked = 0;
    
    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer\n");
        return 0;
    }
    
    if (p->is_in_base) {
        if (die_roll != 6) return 0; // can't come out

        int start = START_POSITION[p->color];
        Cell* cell = &board[start];

        if (cell->count == 0) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        
        // capture if opponent
        if ((cell->count == 1) && (cell->occupants[0]->color != p->color)) {
            if (target_pos) *target_pos = start;
            if (is_capture) *is_capture = 1;
            return 1;
        }
        
        // Can stack with same color pieces
        if (cell->count >= 1 && cell->occupants[0]->color == p->color) {
            if (target_pos) *target_pos = start; 
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0; // blocked by opponent
    }

    if (p->is_in_board) {
        // Safety check for board position
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d for piece %s%d\n", p->board_pos, get_color_name(p->color), p->id);
            return 0;
        }
        
        int new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
        Cell* dest_cell = &board[new_pos];

        // Check if destination is blocked by opponents
        if (dest_cell->count >= 2 && dest_cell->occupants[0]->color != p->color) {
            if (is_capture) *is_capture = 0;
            return 0; // blocked 
        }

        // check for home entry and blocks
        int home_entry = HOME_ENTRY_POSITION[p->color];
        if (DEBUG) printf("DEBUG: Piece %s%d at pos %d, die_roll %d, dist_from_home is %d, dist_from_start is %d, home_entry at %d\n", get_color_name(p->color),
               p->id, p->board_pos, die_roll, p->remaining_to_home-die_roll, p->dist_from_start+die_roll, home_entry);
        
        // Check if the move would cross or land on the home entry position
        int crosses_home_entry = 0;
        int steps_into_home = 0;
        
        for (int i = 0; i <= die_roll; i++) {
            int check_pos = (p->board_pos + i) % STD_BOARD_SIZE; 
            Cell* check_cell = &board[check_pos];
            
            // If there's a block of 2+ opponent pieces in the path
            if (check_cell->count >= 2 && check_cell->occupants[0]->color != p->color) {
                *path_blocked = 1;
                // FIXED: Simplified this logic to avoid negative array access
                if (i > 0) {
                    int prev_pos = (p->board_pos + i - 1) % STD_BOARD_SIZE;
                    Cell *check_previous = &board[prev_pos];
                    if (check_previous->count < 2 || check_previous->occupants[0]->color == p->color) {
                        if (target_pos) *target_pos = prev_pos;
                        if (is_capture && check_previous->count > 0 && check_previous->occupants[0]->color != p->color) {
                            *is_capture = 1;
                        } else if (is_capture) {
                            *is_capture = 0;
                        }
                        return 1;
                    }
                }
                return 0;
            }
// check_pos == home_entry ||
            if ( (p->remaining_to_home) - i <= 0) {
                crosses_home_entry = 1;
                steps_into_home = die_roll - i;
                if (DEBUG) printf("DEBUG: Crosses home entry at step %d, steps_into_home = %d\n", i, steps_into_home);
                break;
            }
        }
        
        // If crossing home entry, check if we can enter home path
        if (crosses_home_entry && steps_into_home > 0) {
            if (steps_into_home >= HOME_PATH) {
                if (DEBUG) printf("DEBUG: Would overshoot home path (steps_into_home=%d, HOME_PATH=%d)\n", 
                       steps_into_home, HOME_PATH);
                return 0; // Overshoot home path
            }
            
            if (DEBUG) printf("DEBUG: Entering home path at index %d\n", steps_into_home);
            if (target_pos) *target_pos = (steps_into_home - 1); 
            if (is_capture) *is_capture = 0;
            return 1;
        }

        // Can capture single opponent piece
        if (dest_cell->count == 1 && dest_cell->occupants[0]->color != p->color) {
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 1;
            return 1;
        }

        // Can move to empty space or stack with same color
        if (dest_cell->count == 0 || 
            (dest_cell->count >= 1 && dest_cell->occupants[0]->color == p->color)) {
            if (target_pos) *target_pos = new_pos;
            if (is_capture) *is_capture = 0;
            return 1;
        }

        return 0;
    }

    if (p->is_in_home_path) {
        // Safety check home index
        if (p->home_index < 0 || p->home_index >= HOME_PATH) {
            if (DEBUG) printf("ERROR: Invalid home index %d for piece %s%d\n", p->home_index, get_color_name(p->color), p->id);
            return 0;
        }
        
        if (p->home_index + die_roll == HOME_PATH) {
            // reaching home (final position)
            if (DEBUG) printf("DEBUG: Reaching final home position\n");
            if (target_pos) *target_pos = HOME_PATH;
            if (is_capture) *is_capture = 0;
            return 1;
        }
        

    }
    return 0;

}

// 2. Fix in apply_move() - Array bounds and logic issues
void apply_move(Piece* p, int die_roll, int target_pos, int is_capture, int* did_capture) {
    *did_capture = 0;
    
    // Safety check
    if (!p) {
        if (DEBUG) printf("ERROR: Null piece pointer in apply_move\n");
        return;
    }

    // from base to start position
    if (p->is_in_base) {
        p->is_in_base = 0;
        p->is_in_board = 1;
        p->board_pos = target_pos;
        p->dist_from_start = 0;  // Just started
        //  Initialize remaining_to_home 
        p->remaining_to_home = STD_BOARD_SIZE - (START_POSITION[p->color] - HOME_ENTRY_POSITION[p->color] + STD_BOARD_SIZE) % STD_BOARD_SIZE + HOME_PATH;

        players[p->color].in_base--;
        players[p->color].in_board++;
        players[p->color].last_move = p;

        Cell* dest_cell = &board[target_pos];
        
        if (is_capture) {
            if (dest_cell->count > 0 && dest_cell->occupants[0]) {
                Piece* victim = dest_cell->occupants[0];
                victim->is_in_base = 1;
                victim->is_in_board = 0;
                victim->board_pos = -1;
                victim->dist_from_start = 0;

                players[victim->color].in_base++;
                players[victim->color].in_board--;
                players[p->color].last_victim = victim;
                dest_cell->occupants[0] = p;
                dest_cell->count = 1;
                *did_capture = 1;
            }
        } else {
            dest_cell->occupants[dest_cell->count++] = p;
        }
        return;
    }

    // movement from board
    if (p->is_in_board) {
        // Safety check
        if (p->board_pos < 0 || p->board_pos >= STD_BOARD_SIZE) {
            if (DEBUG) printf("ERROR: Invalid board position %d in apply_move\n", p->board_pos);
            return;
        }
        
        // Remove from old position
        Cell* old_cell = &board[p->board_pos];
        for (int i = 0; i < old_cell->count; i++) {
            if (old_cell->occupants[i] == p) {
                for (int j = i; j < old_cell->count - 1; j++) {
                    old_cell->occupants[j] = old_cell->occupants[j + 1];
                }
                old_cell->count--;
                break;
            }
        }

        int home_entry = HOME_ENTRY_POSITION[p->color];
        
        // Check if entering home path
        int crosses_home_entry = 0;
        int steps_into_home = 0;
        
        for (int i = 0; i <= die_roll; i++) {
            // int check_pos = (p->board_pos + i) % STD_BOARD_SIZE;
            // check_pos == home_entry ||
            if ( (p->remaining_to_home - i) <= 0) {
                crosses_home_entry = 1;
                steps_into_home = die_roll - i;
                break;
            }
        }
        
        // Entering home path
        if (crosses_home_entry && steps_into_home > 0) {
            if (DEBUG) printf("DEBUG: Applying move to home path, home_index = %d\n", steps_into_home - 1);
            
            // Safety check
            if (steps_into_home <= 0 || steps_into_home > HOME_PATH) {
                if (DEBUG) printf("ERROR: Invalid home index %d when entering home path\n", steps_into_home);
                return;
            }
            
            p->is_in_board = 0;
            p->is_in_home_path = 1;
            p->board_pos = -1;
            p->home_index = steps_into_home - 1;
            p->remaining_to_home = HOME_PATH - steps_into_home;
            players[p->color].last_move = p;
            players[p->color].in_board--;
            return;
        }

        // Regular board movement
        int new_pos = (p->board_pos + die_roll) % STD_BOARD_SIZE;
        Cell* dest_cell = &board[new_pos];
        
        if (is_capture) {
            if (dest_cell->count > 0 && dest_cell->occupants[0]) {
                Piece* victim = dest_cell->occupants[0];
                victim->is_in_base = 1;
                victim->is_in_board = 0;
                victim->board_pos = -1;
                victim->dist_from_start = 0;
                players[victim->color].in_board--;
                players[victim->color].in_base++;
                players[p->color].last_victim = victim;
                dest_cell->occupants[0] = p;
                dest_cell->count = 1;
                *did_capture = 1;
            }
        } else {
            dest_cell->occupants[dest_cell->count++] = p;
        }
        
        p->board_pos = new_pos;
        players[p->color].last_move = p;
        p->dist_from_start = p->dist_from_start + die_roll;
        p->remaining_to_home = p->remaining_to_home - die_roll;
        return;
    }

    // movement within home path
    if (p->is_in_home_path) {
        if (target_pos == HOME_PATH) {
            // Piece has reached home
            if (DEBUG) printf("DEBUG: Piece %s%d reaches final home\n", get_color_name(p->color), p->id);
            p->home_index = -1;
            p->is_in_home = 1;
            p->remaining_to_home = 0;
            p->is_in_home_path = 0;
            p->board_pos = -1;
            p->is_in_board = 0;
            p->is_in_base = 0;
            p->dist_from_start = STD_BOARD_SIZE;

            players[p->color].last_move = p;
            players[p->color].in_home++;
            return;
        }
        

    }
}

int play_red_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int best_capture = 0;
    Outcomes outcome; // for print
    
    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[RED][i].is_in_board) pieces_on_board++;
    }
    
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[RED][i];
        int target, is_capture, path_blocked;
        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
            int priority = 0;
            Outcomes _outcome;
            // Highest priority: Captures
            if (is_capture) {
                priority = 1000 + p->dist_from_start;
                _outcome = CAPTURE;
            }
            // Second priority: Moving pieces in home path
            else if (p->is_in_home_path) {
                priority = 500 + p->home_index;
                _outcome = H_PATH;
            }
            // Third priority: Getting pieces out of base
            else if (p->is_in_base && die_roll == 6 && pieces_on_board < 2) {
                priority = 300;
                _outcome = FR_BASE;
            }
            // Fourth priority: Moving pieces on board
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;
                
                // avoid blocks
                Cell *dest = &board[target];
                if (dest->count >= 1 && dest->occupants[0]->color == RED) {
                    priority -= 60;
                }
                _outcome = ST_PATH;
            }
            // Last priority: Getting pieces out of base
            else if (p->is_in_base && die_roll == 6) {
                priority = 50;
                _outcome = FR_BASE;
            }
            
            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                best_capture = is_capture;
                highest_priority = priority;
                outcome = _outcome;
            }
        }
    }
    
    if (!best_piece) {
        printf("RED cannot move this turn.\n");
        outcome = NON;
        return 0;
    }
    
    int did_capture;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    apply_move(best_piece, die_roll, best_target, best_capture, &did_capture);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    print_outcome(outcome, &players[RED], old_pos, best_target, (did_capture) ? players[RED].last_victim : NULL, &board[best_target] );

    printf("RED moves piece R%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}

int play_green_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function

    
    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[GREEN][i].is_in_board) pieces_on_board++;
    }
    
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[GREEN][i];
        int target, is_capture, path_blocked;
        
        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
            int priority = 0;
            // Highest priority: Block
            Cell *target_cell = &board[target];
            if (target_cell->count > 0 && target_cell->occupants[0]->color==p->color && target != START_POSITION[GREEN]){
                priority= 1000 + p->dist_from_start;
            }

            // High priority: Getting out from base
            else if (p->is_in_base && die_roll == 6) {
                priority = 500;
            }

            // if (is_capture) {
            //     priority = 1000 + p->dist_from_start;
            // }
            // Second priority: Moving pieces in home path

            else if (p->is_in_home_path) {
                priority = 300 + p->home_index;
            }

            // Fourth priority: Moving pieces on board 
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;
                
                // avoid blocks
                Cell *current = &board[p->board_pos];
                if (current->count >= 1 && current->occupants[0]->color == p->color) {
                    priority -= 90;
                }
            }
            // Last priority: capture
            else if (is_capture) {
                priority = 50;
            }
            
            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                captured = is_capture;
                highest_priority = priority;
            }
        }
    }
    
    if (!best_piece) {
        printf("GREEN cannot move this turn.\n");
        return 0;
    }
    
    int did_capture;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    apply_move(best_piece, die_roll, best_target, captured, &did_capture);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    printf("GREEN moves piece G%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}


int play_yellow_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function

    
    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[YELLOW][i].is_in_board) pieces_on_board++;
    }
    
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[YELLOW][i];
        int target, is_capture, path_blocked;
        
        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
            int priority = 0;

            // Highest priority: Getting out from base
            if (p->is_in_base && die_roll == 6) {
                priority = 1000;
            }
            else if (p->is_in_home_path) {
                priority = 500 + p->home_index;
            }
            // High priority: capture
            else if (is_capture) {
                priority = 300 + p->dist_from_start;
            }

            // Next priority: Moving pieces on board 
            else if (p->is_in_board) {
                priority = 100 + p->dist_from_start;
                
                // avoid blocks
                Cell *current = &board[p->board_pos];
                if (current->count >= 1 && current->occupants[0]->color == p->color) {
                    priority -= 10;
                }
            }
            
            if (priority > highest_priority) {
                best_piece = p;
                best_target = target;
                captured = is_capture;
                highest_priority = priority;
            }
        }
    }
    
    if (!best_piece) {
        printf("YELLOW cannot move this turn.\n");
        return 0;
    }
    
    int did_capture;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    apply_move(best_piece, die_roll, best_target, captured, &did_capture);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    printf("YELLOW moves piece Y%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}




int play_blue_turn(int die_roll) {
    Piece* best_piece = NULL;
    int pieces_on_board = 0;
    int highest_priority = -1;
    int best_target = -1;
    int captured = 0; //if capture or not state for the apply move function

    
    // Count pieces on board
    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        if (all_pieces[BLUE][i].is_in_board) pieces_on_board++;
    }

    int last_piece_id = (players[BLUE].last_move)? (players[BLUE].last_move->id) : 4; // 4 because 4%4=0

    for (int i = 0; i < PIECES_PER_PLAYER; i++) {
        Piece *p = &all_pieces[BLUE][(last_piece_id+i)%PIECES_PER_PLAYER];
        int target, is_capture, path_blocked;
        
        if (can_move_piece(p, die_roll, &target, &is_capture, &path_blocked)) {
            int priority = 0;
            best_piece = p;
            best_target = target;
            captured = is_capture;
            break;
            // no priority order yet
            
        }
    }
    
    if (!best_piece) {
        printf("BLUE cannot move this turn.\n");
        return 0;
    }
    
    int did_capture;
    
    // Save initial state for display
    int was_in_base = best_piece->is_in_base;
    int was_in_board = best_piece->is_in_board;
    int was_in_home_path = best_piece->is_in_home_path;
    int old_pos = best_piece->board_pos;
    int old_home_index = best_piece->home_index;

    apply_move(best_piece, die_roll, best_target, captured, &did_capture);

    // Determine source position for display
    int src_pos;
    if (was_in_base) {
        src_pos = 0;  // base
    } else if (was_in_board) {
        src_pos = old_pos;
    } else if (was_in_home_path) {
        src_pos = old_home_index;
    } else {
        src_pos = -1;  // unknown
    }

    printf("BLUE moves piece B%d from %d to %d%s\n",
        best_piece->id,
        src_pos,
        best_target,
        did_capture ? " and gets a bonus roll!" : "");

    return did_capture;
}
