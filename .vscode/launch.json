{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Ludo",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/ludo", // Path to the executable
      "args": [], // Program arguments (if any)
      "stopAtEntry": false, // Set to true to stop at the program's entry point
      "cwd": "${workspaceFolder}", // Current working directory
      "environment": [],
      "externalConsole": false, // Use VS Code's integrated terminal
      "MIMode": "gdb",  // Debugger type
      "miDebuggerPath": "/opt/homebrew/bin/gdb", // Path to the GDB executable (check if this is correct for your system)
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "build" // Run the build task before launching
    }
  ]
}