Yellow player rolls 3
Blue player rolls 6
Red player rolls 4
Green player rolls 5
Blue player has the highest roll and will begin the game.
The order of a single round is Blue, <PERSON>, <PERSON>, Yellow
Blue rolls 5
BLUE cannot move this turn.
Red rolls 1
RED cannot move this turn.
Green rolls 6
GREEN moves piece G1 from 0 to 39
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 1
BLUE cannot move this turn.
Red rolls 5
R<PERSON> cannot move this turn.
Green rolls 5
DEBUG: Piece Green1 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GRE<PERSON> moves piece G1 from 39 to 44
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 4
BLUE cannot move this turn.
Red rolls 3
RED cannot move this turn.
Green rolls 6
DEBUG: Piece Green1 at pos 44, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 37
G<PERSON><PERSON> moves piece G2 from 0 to 39
Yellow rolls 2
YELLOW cannot move this turn.
Blue rolls 1
BLUE cannot move this turn.
Red rolls 2
RED cannot move this turn.
Green rolls 2
DEBUG: Piece Green1 at pos 44, die_roll 2, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
<PERSON><PERSON><PERSON> moves piece G1 from 44 to 46
Yellow rolls 3
Y<PERSON>LOW cannot move this turn.
Blue rolls 1
BLUE cannot move this turn.
Red rolls 6
RED moves piece R1 from 0 to 26
Green rolls 3
DEBUG: Piece Green1 at pos 46, die_roll 3, dist_from_home is 45, dist_from_start is 10, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G1 from 46 to 49
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B1 from 0 to 13
Red rolls 6
DEBUG: Piece Red1 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R2 from 0 to 26
Green rolls 5
DEBUG: Piece Green1 at pos 49, die_roll 5, dist_from_home is 40, dist_from_start is 15, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G1 from 49 to 2
Yellow rolls 2
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B2 from 0 to 13
Red rolls 2
DEBUG: Piece Red1 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R1 from 26 to 28
Green rolls 5
DEBUG: Piece Green1 at pos 2, die_roll 5, dist_from_home is 35, dist_from_start is 20, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G1 from 2 to 7
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 4
DEBUG: Piece Blue1 at pos 13, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 11
BLUE moves piece B1 from 13 to 17
Red rolls 6
DEBUG: Piece Red1 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R1 from 28 to 34
Green rolls 2
DEBUG: Piece Green1 at pos 7, die_roll 2, dist_from_home is 33, dist_from_start is 22, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G1 from 7 to 9
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue2 at pos 13, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 11
BLUE moves piece B2 from 13 to 18
Red rolls 4
DEBUG: Piece Red1 at pos 34, die_roll 4, dist_from_home is 43, dist_from_start is 12, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R1 from 34 to 38
Green rolls 5
DEBUG: Piece Green1 at pos 9, die_roll 5, dist_from_home is 28, dist_from_start is 27, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G1 from 9 to 14
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 17, die_roll 2, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B1 from 17 to 19
Red rolls 1
DEBUG: Piece Red1 at pos 38, die_roll 1, dist_from_home is 42, dist_from_start is 13, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R1 from 38 to 39 and gets a bonus roll!
Green rolls 5
DEBUG: Piece Green1 at pos 14, die_roll 5, dist_from_home is 23, dist_from_start is 32, home_entry at 37
GREEN moves piece G1 from 14 to 19 and gets a bonus roll!
Yellow rolls 6
YELLOW moves piece Y1 from 0 to 0
Blue rolls 2
DEBUG: Piece Blue2 at pos 18, die_roll 2, dist_from_home is 48, dist_from_start is 7, home_entry at 11
BLUE moves piece B2 from 18 to 20
Red rolls 3
DEBUG: Piece Red1 at pos 39, die_roll 3, dist_from_home is 39, dist_from_start is 16, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 24
RED moves piece R1 from 39 to 42
Green rolls 3
DEBUG: Piece Green1 at pos 19, die_roll 3, dist_from_home is 20, dist_from_start is 35, home_entry at 37
GREEN moves piece G1 from 19 to 22
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y1 from 0 to 4
Blue rolls 3
DEBUG: Piece Blue2 at pos 20, die_roll 3, dist_from_home is 45, dist_from_start is 10, home_entry at 11
BLUE moves piece B2 from 20 to 23
Red rolls 5
DEBUG: Piece Red1 at pos 42, die_roll 5, dist_from_home is 34, dist_from_start is 21, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 42 to 47
Green rolls 4
DEBUG: Piece Green1 at pos 22, die_roll 4, dist_from_home is 16, dist_from_start is 39, home_entry at 37
GREEN moves piece G1 from 22 to 26 and gets a bonus roll!
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 4, die_roll 1, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 4 to 5
Blue rolls 4
DEBUG: Piece Blue2 at pos 23, die_roll 4, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B2 from 23 to 27
Red rolls 6
DEBUG: Piece Red1 at pos 47, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 24
RED moves piece R2 from 0 to 26 and gets a bonus roll!
Green rolls 3
GREEN cannot move this turn.
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 5, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 5 to 6
Blue rolls 1
DEBUG: Piece Blue2 at pos 27, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 11
BLUE moves piece B2 from 27 to 28
Red rolls 2
DEBUG: Piece Red1 at pos 47, die_roll 2, dist_from_home is 32, dist_from_start is 23, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R2 from 26 to 28 and gets a bonus roll!
Green rolls 4
GREEN cannot move this turn.
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 6, die_roll 2, dist_from_home is 47, dist_from_start is 8, home_entry at 50
YELLOW moves piece Y1 from 6 to 8
Blue rolls 2
BLUE cannot move this turn.
Red rolls 6
DEBUG: Piece Red1 at pos 47, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R1 from 47 to 1
Green rolls 6
GREEN moves piece G1 from 0 to 39
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 8, die_roll 3, dist_from_home is 44, dist_from_start is 11, home_entry at 50
YELLOW moves piece Y1 from 8 to 11
Blue rolls 2
BLUE cannot move this turn.
Red rolls 4
DEBUG: Piece Red1 at pos 1, die_roll 4, dist_from_home is 24, dist_from_start is 31, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R1 from 1 to 5
Green rolls 4
DEBUG: Piece Green1 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G1 from 39 to 43
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 11, die_roll 4, dist_from_home is 40, dist_from_start is 15, home_entry at 50
YELLOW moves piece Y1 from 11 to 15
Blue rolls 6
BLUE moves piece B3 from 0 to 13
Red rolls 4
DEBUG: Piece Red1 at pos 5, die_roll 4, dist_from_home is 20, dist_from_start is 35, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R1 from 5 to 9
Green rolls 3
DEBUG: Piece Green1 at pos 43, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 37
GREEN moves piece G1 from 43 to 46
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 15, die_roll 1, dist_from_home is 39, dist_from_start is 16, home_entry at 50
YELLOW moves piece Y1 from 15 to 16
Blue rolls 4
DEBUG: Piece Blue3 at pos 13, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 11
BLUE moves piece B3 from 13 to 17
Red rolls 5
DEBUG: Piece Red1 at pos 9, die_roll 5, dist_from_home is 15, dist_from_start is 40, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 5, dist_from_home is 48, dist_from_start is 7, home_entry at 24
RED moves piece R1 from 9 to 14
Green rolls 1
DEBUG: Piece Green1 at pos 46, die_roll 1, dist_from_home is 47, dist_from_start is 8, home_entry at 37
GREEN moves piece G1 from 46 to 47
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 16, die_roll 6, dist_from_home is 33, dist_from_start is 22, home_entry at 50
YELLOW moves piece Y2 from 0 to 0
Blue rolls 6
BLUE moves piece B4 from 0 to 13
Red rolls 4
DEBUG: Piece Red1 at pos 14, die_roll 4, dist_from_home is 11, dist_from_start is 44, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R1 from 14 to 18
Green rolls 5
DEBUG: Piece Green1 at pos 47, die_roll 5, dist_from_home is 42, dist_from_start is 13, home_entry at 37
GREEN moves piece G1 from 47 to 0 and gets a bonus roll!
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 16, die_roll 3, dist_from_home is 36, dist_from_start is 19, home_entry at 50
YELLOW moves piece Y1 from 16 to 19
Blue rolls 1
DEBUG: Piece Blue3 at pos 17, die_roll 1, dist_from_home is 50, dist_from_start is 5, home_entry at 11
BLUE moves piece B3 from 17 to 18 and gets a bonus roll!
Red rolls 6
DEBUG: Piece Red2 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R1 from 0 to 26
Green rolls 1
DEBUG: Piece Green1 at pos 0, die_roll 1, dist_from_home is 41, dist_from_start is 14, home_entry at 37
GREEN moves piece G1 from 0 to 1
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 19, die_roll 4, dist_from_home is 32, dist_from_start is 23, home_entry at 50
YELLOW moves piece Y1 from 19 to 23
Blue rolls 6
DEBUG: Piece Blue4 at pos 13, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B4 from 13 to 19
Red rolls 2
DEBUG: Piece Red1 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 2, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R2 from 28 to 30
Green rolls 5
DEBUG: Piece Green1 at pos 1, die_roll 5, dist_from_home is 36, dist_from_start is 19, home_entry at 37
GREEN moves piece G1 from 1 to 6
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 23, die_roll 3, dist_from_home is 29, dist_from_start is 26, home_entry at 50
YELLOW moves piece Y1 from 23 to 26 and gets a bonus roll!
Blue rolls 6
BLUE moves piece B1 from 0 to 13
Red rolls 1
DEBUG: Piece Red2 at pos 30, die_roll 1, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R2 from 30 to 31
Green rolls 5
DEBUG: Piece Green1 at pos 6, die_roll 5, dist_from_home is 31, dist_from_start is 24, home_entry at 37
GREEN moves piece G1 from 6 to 11
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 26, die_roll 2, dist_from_home is 27, dist_from_start is 28, home_entry at 50
YELLOW moves piece Y1 from 26 to 28
Blue rolls 6
BLUE moves piece B2 from 0 to 13
Red rolls 3
DEBUG: Piece Red2 at pos 31, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R2 from 31 to 34
Green rolls 6
DEBUG: Piece Green1 at pos 11, die_roll 6, dist_from_home is 25, dist_from_start is 30, home_entry at 37
GREEN moves piece G2 from 0 to 39
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 28, die_roll 5, dist_from_home is 22, dist_from_start is 33, home_entry at 50
YELLOW moves piece Y1 from 28 to 33
Blue rolls 4
DEBUG: Piece Blue3 at pos 18, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 11
BLUE moves piece B3 from 18 to 22
Red rolls 1
DEBUG: Piece Red2 at pos 34, die_roll 1, dist_from_home is 46, dist_from_start is 9, home_entry at 24
RED moves piece R2 from 34 to 35
Green rolls 1
DEBUG: Piece Green1 at pos 11, die_roll 1, dist_from_home is 30, dist_from_start is 25, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G1 from 11 to 12
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 33, die_roll 1, dist_from_home is 21, dist_from_start is 34, home_entry at 50
YELLOW moves piece Y1 from 33 to 34
Blue rolls 4
DEBUG: Piece Blue4 at pos 19, die_roll 4, dist_from_home is 45, dist_from_start is 10, home_entry at 11
BLUE moves piece B4 from 19 to 23
Red rolls 6
DEBUG: Piece Red2 at pos 35, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 24
RED moves piece R1 from 0 to 26
Green rolls 4
DEBUG: Piece Green1 at pos 12, die_roll 4, dist_from_home is 26, dist_from_start is 29, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G1 from 12 to 12
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 34, die_roll 2, dist_from_home is 19, dist_from_start is 36, home_entry at 50
YELLOW moves piece Y1 from 34 to 36
Blue rolls 1
DEBUG: Piece Blue1 at pos 13, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 11
BLUE moves piece B1 from 13 to 14
Red rolls 1
DEBUG: Piece Red1 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
DEBUG: Piece Red2 at pos 35, die_roll 1, dist_from_home is 45, dist_from_start is 10, home_entry at 24
RED moves piece R2 from 35 to 36 and gets a bonus roll!
Green rolls 6
DEBUG: Piece Green1 at pos 16, die_roll 6, dist_from_home is 20, dist_from_start is 35, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G3 from 0 to 39
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue2 at pos 13, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 11
BLUE moves piece B2 from 13 to 18
Red rolls 1
DEBUG: Piece Red1 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
DEBUG: Piece Red2 at pos 36, die_roll 1, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R2 from 36 to 37
Green rolls 6
DEBUG: Piece Green1 at pos 16, die_roll 6, dist_from_home is 20, dist_from_start is 35, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G4 from 0 to 39
Yellow rolls 6
YELLOW moves piece Y1 from 0 to 0
Blue rolls 5
DEBUG: Piece Blue3 at pos 22, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B3 from 22 to 27
Red rolls 6
DEBUG: Piece Red1 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Piece Red2 at pos 37, die_roll 6, dist_from_home is 38, dist_from_start is 17, home_entry at 24
RED moves piece R2 from 37 to 38
Green rolls 4
DEBUG: Piece Green1 at pos 16, die_roll 4, dist_from_home is 22, dist_from_start is 33, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G1 from 16 to 20
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y1 from 0 to 2
Blue rolls 2
DEBUG: Piece Blue4 at pos 23, die_roll 2, dist_from_home is 43, dist_from_start is 12, home_entry at 11
BLUE moves piece B4 from 23 to 25
Red rolls 4
DEBUG: Piece Red1 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
DEBUG: Piece Red2 at pos 43, die_roll 4, dist_from_home is 34, dist_from_start is 21, home_entry at 24
RED moves piece R2 from 43 to 47
Green rolls 2
DEBUG: Piece Green1 at pos 20, die_roll 2, dist_from_home is 20, dist_from_start is 35, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G1 from 20 to 22
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 2, die_roll 2, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y1 from 2 to 4
Blue rolls 1
DEBUG: Piece Blue1 at pos 14, die_roll 1, dist_from_home is 53, dist_from_start is 2, home_entry at 11
BLUE moves piece B1 from 14 to 15
Red rolls 6
DEBUG: Piece Red1 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Piece Red2 at pos 47, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 24
RED moves piece R2 from 47 to 1
Green rolls 6
DEBUG: Piece Green1 at pos 22, die_roll 6, dist_from_home is 14, dist_from_start is 41, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G1 from 22 to 28
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 4, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 50
YELLOW moves piece Y1 from 4 to 7
Blue rolls 3
DEBUG: Piece Blue2 at pos 18, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 11
BLUE moves piece B2 from 18 to 21
Red rolls 3
DEBUG: Piece Red1 at pos 26, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 24
DEBUG: Piece Red2 at pos 1, die_roll 3, dist_from_home is 25, dist_from_start is 30, home_entry at 24
RED moves piece R2 from 1 to 4
Green rolls 5
DEBUG: Piece Green1 at pos 28, die_roll 5, dist_from_home is 9, dist_from_start is 46, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G1 from 28 to 33
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 7, die_roll 6, dist_from_home is 42, dist_from_start is 13, home_entry at 50
YELLOW moves piece Y2 from 0 to 0
Blue rolls 4
DEBUG: Piece Blue3 at pos 27, die_roll 4, dist_from_home is 37, dist_from_start is 18, home_entry at 11
BLUE moves piece B3 from 27 to 31
Red rolls 6
DEBUG: Piece Red1 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Piece Red2 at pos 4, die_roll 6, dist_from_home is 19, dist_from_start is 36, home_entry at 24
RED moves piece R2 from 4 to 10
Green rolls 3
DEBUG: Piece Green1 at pos 33, die_roll 3, dist_from_home is 6, dist_from_start is 49, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G1 from 33 to 36
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 7, die_roll 6, dist_from_home is 42, dist_from_start is 13, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y3 from 0 to 0
Blue rolls 1
DEBUG: Piece Blue4 at pos 25, die_roll 1, dist_from_home is 42, dist_from_start is 13, home_entry at 11
BLUE moves piece B4 from 25 to 26 and gets a bonus roll!
Red rolls 1
DEBUG: Piece Red2 at pos 10, die_roll 1, dist_from_home is 18, dist_from_start is 37, home_entry at 24
RED moves piece R2 from 10 to 11
Green rolls 2
DEBUG: Piece Green1 at pos 36, die_roll 2, dist_from_home is 4, dist_from_start is 51, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G1 from 36 to 38
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 7, die_roll 4, dist_from_home is 44, dist_from_start is 11, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y1 from 7 to 11 and gets a bonus roll!
Blue rolls 3
DEBUG: Piece Blue1 at pos 15, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 11
BLUE moves piece B1 from 15 to 18
Red rolls 3
RED cannot move this turn.
Green rolls 2
DEBUG: Piece Green1 at pos 38, die_roll 2, dist_from_home is 2, dist_from_start is 53, home_entry at 37
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G1 from 38 to 40
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 11, die_roll 5, dist_from_home is 39, dist_from_start is 16, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 11 to 16
Blue rolls 3
DEBUG: Piece Blue2 at pos 21, die_roll 3, dist_from_home is 44, dist_from_start is 11, home_entry at 11
BLUE moves piece B2 from 21 to 24
Red rolls 1
RED cannot move this turn.
Green rolls 6
DEBUG: Piece Green1 at pos 40, die_roll 6, dist_from_home is -4, dist_from_start is 59, home_entry at 37
DEBUG: Crosses home entry at step 2, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Applying move to home path, home_index = 3
GREEN moves piece G1 from 40 to 3
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 16, die_roll 3, dist_from_home is 36, dist_from_start is 19, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 16 to 19
Blue rolls 5
DEBUG: Piece Blue3 at pos 31, die_roll 5, dist_from_home is 32, dist_from_start is 23, home_entry at 11
BLUE moves piece B3 from 31 to 36
Red rolls 4
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green2 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 39 to 42
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 19, die_roll 3, dist_from_home is 33, dist_from_start is 22, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 19 to 22
Blue rolls 1
DEBUG: Piece Blue4 at pos 26, die_roll 1, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B4 from 26 to 27
Red rolls 6
RED moves piece R1 from 0 to 26
Green rolls 6
DEBUG: Piece Green2 at pos 42, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 42 to 48
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 22, die_roll 3, dist_from_home is 30, dist_from_start is 25, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 22 to 25
Blue rolls 1
DEBUG: Piece Blue1 at pos 18, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B1 from 18 to 19
Red rolls 5
DEBUG: Piece Red1 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 26 to 31
Green rolls 1
DEBUG: Piece Green2 at pos 48, die_roll 1, dist_from_home is 45, dist_from_start is 10, home_entry at 37
DEBUG: Piece Green3 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 48 to 49
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 25, die_roll 1, dist_from_home is 29, dist_from_start is 26, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y1 from 25 to 26
Blue rolls 3
DEBUG: Piece Blue2 at pos 24, die_roll 3, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B2 from 24 to 27
Red rolls 6
DEBUG: Piece Red1 at pos 31, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R2 from 0 to 26 and gets a bonus roll!
Green rolls 3
DEBUG: Piece Green3 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G3 from 39 to 42
Yellow rolls 1
DEBUG: Piece Yellow2 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y2 from 0 to 1
Blue rolls 2
DEBUG: Piece Blue3 at pos 36, die_roll 2, dist_from_home is 30, dist_from_start is 25, home_entry at 11
BLUE moves piece B3 from 36 to 38
Red rolls 4
DEBUG: Piece Red1 at pos 31, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R1 from 31 to 35
Green rolls 2
DEBUG: Reaching final home position
DEBUG: Piece Green2 at pos 49, die_roll 2, dist_from_home is 43, dist_from_start is 12, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 2, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green1 reaches final home
GREEN moves piece G1 from 3 to 5
Yellow rolls 1
DEBUG: Piece Yellow2 at pos 1, die_roll 1, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y2 from 1 to 2
Blue rolls 2
DEBUG: Piece Blue4 at pos 27, die_roll 2, dist_from_home is 39, dist_from_start is 16, home_entry at 11
BLUE moves piece B4 from 27 to 29
Red rolls 5
DEBUG: Piece Red1 at pos 35, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 35 to 40
Green rolls 1
DEBUG: Piece Green2 at pos 49, die_roll 1, dist_from_home is 44, dist_from_start is 11, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 49 to 50
Yellow rolls 6
DEBUG: Piece Yellow2 at pos 2, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 0 to 0
Blue rolls 4
DEBUG: Piece Blue1 at pos 19, die_roll 4, dist_from_home is 45, dist_from_start is 10, home_entry at 11
BLUE moves piece B1 from 19 to 23
Red rolls 5
DEBUG: Piece Red1 at pos 40, die_roll 5, dist_from_home is 36, dist_from_start is 19, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 40 to 45
Green rolls 1
DEBUG: Piece Green2 at pos 50, die_roll 1, dist_from_home is 43, dist_from_start is 12, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 50 to 51
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow2 at pos 2, die_roll 5, dist_from_home is 48, dist_from_start is 7, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y2 from 2 to 7
Blue rolls 1
DEBUG: Piece Blue2 at pos 27, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 11
BLUE moves piece B2 from 27 to 28
Red rolls 2
DEBUG: Piece Red1 at pos 45, die_roll 2, dist_from_home is 34, dist_from_start is 21, home_entry at 24
DEBUG: Piece Red2 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R2 from 26 to 28 and gets a bonus roll!
Green rolls 4
DEBUG: Piece Green2 at pos 51, die_roll 4, dist_from_home is 39, dist_from_start is 16, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 4, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 51 to 51
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow2 at pos 7, die_roll 1, dist_from_home is 47, dist_from_start is 8, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y2 from 7 to 8
Blue rolls 5
DEBUG: Piece Blue3 at pos 38, die_roll 5, dist_from_home is 25, dist_from_start is 30, home_entry at 11
BLUE moves piece B3 from 38 to 43
Red rolls 3
DEBUG: Piece Red1 at pos 45, die_roll 3, dist_from_home is 33, dist_from_start is 22, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 45 to 48
Green rolls 5
DEBUG: Piece Green2 at pos 3, die_roll 5, dist_from_home is 34, dist_from_start is 21, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 5, dist_from_home is 47, dist_from_start is 8, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G2 from 3 to 8 and gets a bonus roll!
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y2 from 0 to 0
Blue rolls 5
DEBUG: Piece Blue4 at pos 29, die_roll 5, dist_from_home is 34, dist_from_start is 21, home_entry at 11
BLUE moves piece B4 from 29 to 34
Red rolls 3
DEBUG: Piece Red1 at pos 48, die_roll 3, dist_from_home is 30, dist_from_start is 25, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 48 to 51
Green rolls 6
DEBUG: Piece Green2 at pos 8, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 8 to 14
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y4 from 0 to 0
Blue rolls 4
DEBUG: Piece Blue1 at pos 23, die_roll 4, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B1 from 23 to 27
Red rolls 6
DEBUG: Piece Red1 at pos 51, die_roll 6, dist_from_home is 24, dist_from_start is 31, home_entry at 24
DEBUG: Piece Red2 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R2 from 28 to 34 and gets a bonus roll!
Green rolls 4
DEBUG: Piece Green2 at pos 14, die_roll 4, dist_from_home is 24, dist_from_start is 31, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 4, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 14 to 18
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y1 from 0 to 4
Blue rolls 3
DEBUG: Piece Blue3 at pos 43, die_roll 3, dist_from_home is 22, dist_from_start is 33, home_entry at 11
BLUE moves piece B3 from 43 to 46
Red rolls 5
DEBUG: Piece Red1 at pos 51, die_roll 5, dist_from_home is 25, dist_from_start is 30, home_entry at 24
DEBUG: Piece Red2 at pos 34, die_roll 5, dist_from_home is 42, dist_from_start is 13, home_entry at 24
RED moves piece R2 from 34 to 39 and gets a bonus roll!
Green rolls 6
DEBUG: Piece Green2 at pos 18, die_roll 6, dist_from_home is 18, dist_from_start is 37, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 37
GREEN moves piece G4 from 0 to 39 and gets a bonus roll!
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 4, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 4 to 7
Blue rolls 1
DEBUG: Piece Blue1 at pos 27, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 11
BLUE moves piece B1 from 27 to 28
Red rolls 4
DEBUG: Piece Red1 at pos 51, die_roll 4, dist_from_home is 26, dist_from_start is 29, home_entry at 24
RED moves piece R1 from 51 to 51
Green rolls 1
DEBUG: Piece Green2 at pos 18, die_roll 1, dist_from_home is 23, dist_from_start is 32, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 18 to 19
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 7, die_roll 3, dist_from_home is 45, dist_from_start is 10, home_entry at 50
DEBUG: Piece Yellow2 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y2 from 0 to 3 and gets a bonus roll!
Blue rolls 2
DEBUG: Piece Blue3 at pos 46, die_roll 2, dist_from_home is 20, dist_from_start is 35, home_entry at 11
BLUE moves piece B3 from 46 to 48
Red rolls 4
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green2 at pos 19, die_roll 4, dist_from_home is 19, dist_from_start is 36, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 4, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 19 to 23
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 7, die_roll 6, dist_from_home is 42, dist_from_start is 13, home_entry at 50
DEBUG: Piece Yellow2 at pos 3, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 7 to 13
Blue rolls 6
BLUE moves piece B4 from 0 to 13 and gets a bonus roll!
Red rolls 3
RED cannot move this turn.
Green rolls 6
DEBUG: Piece Green2 at pos 23, die_roll 6, dist_from_home is 13, dist_from_start is 42, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 23 to 29
Yellow rolls 6
DEBUG: Piece Yellow2 at pos 3, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 0 to 0
Blue rolls 4
DEBUG: Piece Blue1 at pos 28, die_roll 4, dist_from_home is 36, dist_from_start is 19, home_entry at 11
BLUE moves piece B1 from 28 to 32
Red rolls 1
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green2 at pos 29, die_roll 4, dist_from_home is 9, dist_from_start is 46, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 4, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 29 to 33
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 at pos 3, die_roll 2, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y2 from 3 to 5
Blue rolls 4
DEBUG: Piece Blue4 at pos 13, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 11
BLUE moves piece B4 from 13 to 17
Red rolls 5
RED cannot move this turn.
Green rolls 1
DEBUG: Piece Green2 at pos 33, die_roll 1, dist_from_home is 8, dist_from_start is 47, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 33 to 34
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow2 at pos 5, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y2 from 5 to 8
Blue rolls 5
DEBUG: Piece Blue1 at pos 32, die_roll 5, dist_from_home is 31, dist_from_start is 24, home_entry at 11
BLUE moves piece B1 from 32 to 37
Red rolls 2
RED cannot move this turn.
Green rolls 1
DEBUG: Piece Green2 at pos 34, die_roll 1, dist_from_home is 7, dist_from_start is 48, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 34 to 35
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow2 at pos 8, die_roll 1, dist_from_home is 46, dist_from_start is 9, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y2 from 8 to 9
Blue rolls 4
DEBUG: Piece Blue4 at pos 17, die_roll 4, dist_from_home is 47, dist_from_start is 8, home_entry at 11
BLUE moves piece B4 from 17 to 21
Red rolls 3
RED cannot move this turn.
Green rolls 5
DEBUG: Piece Green2 at pos 35, die_roll 5, dist_from_home is 2, dist_from_start is 53, home_entry at 37
DEBUG: Piece Green3 at pos 42, die_roll 5, dist_from_home is 47, dist_from_start is 8, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G2 from 35 to 40
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow2 at pos 9, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y2 from 9 to 15
Blue rolls 3
DEBUG: Piece Blue1 at pos 37, die_roll 3, dist_from_home is 28, dist_from_start is 27, home_entry at 11
BLUE moves piece B1 from 37 to 40 and gets a bonus roll!
Red rolls 4
RED cannot move this turn.
Green rolls 5
DEBUG: Piece Green3 at pos 42, die_roll 5, dist_from_home is 47, dist_from_start is 8, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G3 from 42 to 47
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow2 at pos 15, die_roll 3, dist_from_home is 37, dist_from_start is 18, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y2 from 15 to 18
Blue rolls 2
DEBUG: Piece Blue3 at pos 48, die_roll 2, dist_from_home is 18, dist_from_start is 37, home_entry at 11
BLUE moves piece B3 from 48 to 50
Red rolls 2
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green3 at pos 47, die_roll 4, dist_from_home is 43, dist_from_start is 12, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G3 from 47 to 51
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 at pos 18, die_roll 2, dist_from_home is 35, dist_from_start is 20, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y2 from 18 to 20
Blue rolls 4
DEBUG: Piece Blue4 at pos 21, die_roll 4, dist_from_home is 43, dist_from_start is 12, home_entry at 11
BLUE moves piece B4 from 21 to 25
Red rolls 3
RED cannot move this turn.
Green rolls 6
DEBUG: Piece Green3 at pos 51, die_roll 6, dist_from_home is 37, dist_from_start is 18, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G3 from 51 to 51
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow2 at pos 20, die_roll 6, dist_from_home is 29, dist_from_start is 26, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y2 from 20 to 26
Blue rolls 5
DEBUG: Piece Blue1 at pos 40, die_roll 5, dist_from_home is 23, dist_from_start is 32, home_entry at 11
BLUE moves piece B1 from 40 to 45
Red rolls 2
RED cannot move this turn.
Green rolls 2
DEBUG: Piece Green3 at pos 5, die_roll 2, dist_from_home is 35, dist_from_start is 20, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G3 from 5 to 7
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow2 at pos 26, die_roll 6, dist_from_home is 23, dist_from_start is 32, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y2 from 26 to 32
Blue rolls 1
DEBUG: Piece Blue3 at pos 50, die_roll 1, dist_from_home is 17, dist_from_start is 38, home_entry at 11
BLUE moves piece B3 from 50 to 51
Red rolls 3
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green3 at pos 7, die_roll 3, dist_from_home is 32, dist_from_start is 23, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G3 from 7 to 10
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow2 at pos 32, die_roll 5, dist_from_home is 18, dist_from_start is 37, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y2 from 32 to 37
Blue rolls 5
DEBUG: Piece Blue4 at pos 25, die_roll 5, dist_from_home is 38, dist_from_start is 17, home_entry at 11
BLUE moves piece B4 from 25 to 30
Red rolls 5
RED cannot move this turn.
Green rolls 6
DEBUG: Piece Green3 at pos 10, die_roll 6, dist_from_home is 26, dist_from_start is 29, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 0 to 39
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow2 at pos 37, die_roll 3, dist_from_home is 15, dist_from_start is 40, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y2 from 37 to 38
Blue rolls 2
DEBUG: Piece Blue1 at pos 45, die_roll 2, dist_from_home is 21, dist_from_start is 34, home_entry at 11
BLUE moves piece B1 from 45 to 47
Red rolls 2
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green2 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green3 at pos 10, die_roll 4, dist_from_home is 28, dist_from_start is 27, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G3 from 10 to 14
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 at pos 40, die_roll 2, dist_from_home is 13, dist_from_start is 42, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y2 from 40 to 42
Blue rolls 5
DEBUG: Piece Blue3 at pos 51, die_roll 5, dist_from_home is 12, dist_from_start is 43, home_entry at 11
BLUE moves piece B3 from 51 to 51
Red rolls 5
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green2 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
DEBUG: Piece Green3 at pos 14, die_roll 3, dist_from_home is 25, dist_from_start is 30, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G3 from 14 to 17
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow2 at pos 42, die_roll 1, dist_from_home is 12, dist_from_start is 43, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y2 from 42 to 43
Blue rolls 2
DEBUG: Piece Blue4 at pos 30, die_roll 2, dist_from_home is 36, dist_from_start is 19, home_entry at 11
BLUE moves piece B4 from 30 to 32
Red rolls 5
RED cannot move this turn.
Green rolls 5
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green3 at pos 17, die_roll 5, dist_from_home is 20, dist_from_start is 35, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G3 from 17 to 22
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow2 at pos 43, die_roll 4, dist_from_home is 8, dist_from_start is 47, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y2 from 43 to 47 and gets a bonus roll!
Blue rolls 5
DEBUG: Piece Blue3 at pos 4, die_roll 5, dist_from_home is 7, dist_from_start is 48, home_entry at 11
BLUE moves piece B3 from 4 to 9
Red rolls 2
RED cannot move this turn.
Green rolls 2
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green3 at pos 22, die_roll 2, dist_from_home is 18, dist_from_start is 37, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G3 from 22 to 24
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 at pos 47, die_roll 2, dist_from_home is 6, dist_from_start is 49, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y2 from 47 to 49
Blue rolls 2
DEBUG: Piece Blue4 at pos 32, die_roll 2, dist_from_home is 34, dist_from_start is 21, home_entry at 11
BLUE moves piece B4 from 32 to 34
Red rolls 2
RED cannot move this turn.
Green rolls 1
DEBUG: Piece Green2 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
DEBUG: Piece Green3 at pos 24, die_roll 1, dist_from_home is 17, dist_from_start is 38, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G3 from 24 to 25
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow2 at pos 49, die_roll 4, dist_from_home is 2, dist_from_start is 53, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y2 from 49 to 1
Blue rolls 4
DEBUG: Piece Blue3 at pos 9, die_roll 4, dist_from_home is 3, dist_from_start is 52, home_entry at 11
BLUE moves piece B3 from 9 to 13
Red rolls 5
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green2 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
DEBUG: Piece Green3 at pos 25, die_roll 4, dist_from_home is 13, dist_from_start is 42, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G3 from 25 to 29
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 at pos 1, die_roll 2, dist_from_home is 0, dist_from_start is 55, home_entry at 50
DEBUG: Crosses home entry at step 2, steps_into_home = 0
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y2 from 1 to 3
Blue rolls 5
DEBUG: Piece Blue3 at pos 13, die_roll 5, dist_from_home is -2, dist_from_start is 57, home_entry at 11
DEBUG: Crosses home entry at step 3, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Applying move to home path, home_index = 1
BLUE moves piece B3 from 13 to 1
Red rolls 6
RED moves piece R1 from 0 to 26
Green rolls 6
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green3 at pos 29, die_roll 6, dist_from_home is 7, dist_from_start is 48, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G3 from 29 to 35
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow2 at pos 3, die_roll 4, dist_from_home is -4, dist_from_start is 59, home_entry at 50
DEBUG: Crosses home entry at step 0, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Applying move to home path, home_index = 3
YELLOW moves piece Y2 from 3 to 3
Blue rolls 6
DEBUG: Piece Blue4 at pos 34, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 11
BLUE moves piece B4 from 34 to 38
Red rolls 5
DEBUG: Piece Red1 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R1 from 26 to 31
Green rolls 6
DEBUG: Piece Green2 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Piece Green3 at pos 35, die_roll 6, dist_from_home is 1, dist_from_start is 54, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G3 from 35 to 41
Yellow rolls 4
DEBUG: Piece Yellow1 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 50
YELLOW moves piece Y1 from 0 to 4
Blue rolls 6
BLUE moves piece B1 from 0 to 13
Red rolls 3
DEBUG: Piece Red1 at pos 31, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R1 from 31 to 34
Green rolls 2
DEBUG: Piece Green2 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
DEBUG: Piece Green3 at pos 41, die_roll 2, dist_from_home is -1, dist_from_start is 56, home_entry at 37
DEBUG: Crosses home entry at step 1, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G2 from 39 to 41
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 4, die_roll 2, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Reaching final home position
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
DEBUG: Piece Yellow2 reaches final home
YELLOW moves piece Y2 from 3 to 5
Blue rolls 6
BLUE moves piece B2 from 0 to 13
Red rolls 3
DEBUG: Piece Red1 at pos 34, die_roll 3, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R1 from 34 to 37
Green rolls 5
DEBUG: Piece Green2 at pos 41, die_roll 5, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green3 at pos 41, die_roll 5, dist_from_home is -4, dist_from_start is 59, home_entry at 37
DEBUG: Crosses home entry at step 1, steps_into_home = 4
DEBUG: Entering home path at index 4
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Applying move to home path, home_index = 3
GREEN moves piece G3 from 41 to 3
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 4, die_roll 5, dist_from_home is 46, dist_from_start is 9, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 4 to 9
Blue rolls 1
DEBUG: Piece Blue4 at pos 40, die_roll 1, dist_from_home is 27, dist_from_start is 28, home_entry at 11
BLUE moves piece B4 from 40 to 41 and gets a bonus roll!
Red rolls 4
DEBUG: Piece Red1 at pos 37, die_roll 4, dist_from_home is 40, dist_from_start is 15, home_entry at 24
RED moves piece R1 from 37 to 41 and gets a bonus roll!
Green rolls 6
DEBUG: Piece Green4 at pos 39, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 0 to 39
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 9, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 9 to 12
Blue rolls 2
DEBUG: Piece Blue1 at pos 13, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 11
BLUE moves piece B1 from 13 to 15 and gets a bonus roll!
Red rolls 4
DEBUG: Piece Red1 at pos 41, die_roll 4, dist_from_home is 36, dist_from_start is 19, home_entry at 24
RED moves piece R1 from 41 to 45
Green rolls 5
DEBUG: Piece Green2 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G2 from 39 to 44
Yellow rolls 6
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 0 to 0
Blue rolls 3
DEBUG: Piece Blue2 at pos 13, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 11
BLUE moves piece B2 from 13 to 16
Red rolls 3
DEBUG: Piece Red1 at pos 45, die_roll 3, dist_from_home is 33, dist_from_start is 22, home_entry at 24
RED moves piece R1 from 45 to 48
Green rolls 5
DEBUG: Piece Green2 at pos 44, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G4 from 39 to 44
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 0 to 6
Blue rolls 2
DEBUG: Piece Blue1 at pos 15, die_roll 2, dist_from_home is 51, dist_from_start is 4, home_entry at 11
BLUE moves piece B1 from 15 to 17
Red rolls 6
DEBUG: Piece Red1 at pos 48, die_roll 6, dist_from_home is 27, dist_from_start is 28, home_entry at 24
RED moves piece R2 from 0 to 26
Green rolls 4
DEBUG: Piece Green2 at pos 44, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 37
GREEN moves piece G2 from 44 to 48 and gets a bonus roll!
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 6, die_roll 5, dist_from_home is 44, dist_from_start is 11, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 6 to 11
Blue rolls 3
DEBUG: Piece Blue2 at pos 16, die_roll 3, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B2 from 16 to 19
Red rolls 4
DEBUG: Piece Red2 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R2 from 26 to 30
Green rolls 5
DEBUG: Piece Green2 at pos 48, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 37
GREEN moves piece G2 from 48 to 51
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 11, die_roll 3, dist_from_home is 41, dist_from_start is 14, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 11 to 14
Blue rolls 3
DEBUG: Piece Blue1 at pos 17, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 11
BLUE moves piece B1 from 17 to 20
Red rolls 5
DEBUG: Piece Red2 at pos 30, die_roll 5, dist_from_home is 46, dist_from_start is 9, home_entry at 24
RED moves piece R2 from 30 to 35
Green rolls 3
DEBUG: Piece Green2 at pos 1, die_roll 3, dist_from_home is 38, dist_from_start is 17, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 37
GREEN moves piece G2 from 1 to 4
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 14, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y1 from 14 to 15
Blue rolls 2
DEBUG: Piece Blue2 at pos 19, die_roll 2, dist_from_home is 47, dist_from_start is 8, home_entry at 11
BLUE moves piece B2 from 19 to 21
Red rolls 5
DEBUG: Piece Red2 at pos 35, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 24
RED moves piece R2 from 35 to 40
Green rolls 5
DEBUG: Piece Green2 at pos 4, die_roll 5, dist_from_home is 33, dist_from_start is 22, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 37
GREEN moves piece G2 from 4 to 9
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 15, die_roll 6, dist_from_home is 34, dist_from_start is 21, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 15 to 21 and gets a bonus roll!
Blue rolls 2
DEBUG: Piece Blue1 at pos 20, die_roll 2, dist_from_home is 46, dist_from_start is 9, home_entry at 11
BLUE moves piece B1 from 20 to 22
Red rolls 1
DEBUG: Piece Red2 at pos 40, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 24
RED moves piece R2 from 40 to 41
Green rolls 6
DEBUG: Piece Green2 at pos 9, die_roll 6, dist_from_home is 27, dist_from_start is 28, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 37
GREEN moves piece G2 from 9 to 15
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 21, die_roll 6, dist_from_home is 28, dist_from_start is 27, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y1 from 21 to 27
Blue rolls 5
DEBUG: Piece Blue1 at pos 22, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B1 from 22 to 27 and gets a bonus roll!
Red rolls 4
DEBUG: Piece Red2 at pos 41, die_roll 4, dist_from_home is 36, dist_from_start is 19, home_entry at 24
RED moves piece R2 from 41 to 45
Green rolls 1
DEBUG: Piece Green2 at pos 15, die_roll 1, dist_from_home is 26, dist_from_start is 29, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 15 to 16
Yellow rolls 3
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y3 from 0 to 3
Blue rolls 3
DEBUG: Piece Blue1 at pos 27, die_roll 3, dist_from_home is 38, dist_from_start is 17, home_entry at 11
BLUE moves piece B1 from 27 to 30
Red rolls 4
DEBUG: Piece Red2 at pos 45, die_roll 4, dist_from_home is 32, dist_from_start is 23, home_entry at 24
RED moves piece R2 from 45 to 49
Green rolls 2
DEBUG: Piece Green2 at pos 16, die_roll 2, dist_from_home is 24, dist_from_start is 31, home_entry at 37
DEBUG: Reaching final home position
DEBUG: Piece Green4 at pos 44, die_roll 2, dist_from_home is 48, dist_from_start is 7, home_entry at 37
DEBUG: Piece Green3 reaches final home
GREEN moves piece G3 from 3 to 5
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 3, die_roll 2, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y3 from 3 to 5
Blue rolls 1
DEBUG: Piece Blue1 at pos 30, die_roll 1, dist_from_home is 37, dist_from_start is 18, home_entry at 11
BLUE moves piece B1 from 30 to 31
Red rolls 1
DEBUG: Piece Red2 at pos 49, die_roll 1, dist_from_home is 31, dist_from_start is 24, home_entry at 24
RED moves piece R2 from 49 to 50
Green rolls 6
DEBUG: Piece Green2 at pos 16, die_roll 6, dist_from_home is 20, dist_from_start is 35, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 37
GREEN moves piece G2 from 16 to 22
Yellow rolls 1
DEBUG: Piece Yellow3 at pos 5, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 50
YELLOW moves piece Y3 from 5 to 6
Blue rolls 3
DEBUG: Piece Blue1 at pos 31, die_roll 3, dist_from_home is 34, dist_from_start is 21, home_entry at 11
BLUE moves piece B1 from 31 to 34
Red rolls 2
DEBUG: Piece Red2 at pos 50, die_roll 2, dist_from_home is 29, dist_from_start is 26, home_entry at 24
RED moves piece R2 from 50 to 0 and gets a bonus roll!
Green rolls 3
DEBUG: Piece Green2 at pos 22, die_roll 3, dist_from_home is 17, dist_from_start is 38, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 37
GREEN moves piece G2 from 22 to 25
Yellow rolls 5
DEBUG: Piece Yellow3 at pos 6, die_roll 5, dist_from_home is 44, dist_from_start is 11, home_entry at 50
YELLOW moves piece Y3 from 6 to 11
Blue rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Blue3 reaches final home
BLUE moves piece B3 from 1 to 5
Red rolls 6
DEBUG: Piece Red2 at pos 0, die_roll 6, dist_from_home is 23, dist_from_start is 32, home_entry at 24
RED moves piece R1 from 0 to 26
Green rolls 3
DEBUG: Piece Green2 at pos 25, die_roll 3, dist_from_home is 14, dist_from_start is 41, home_entry at 37
DEBUG: Piece Green4 at pos 44, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 37
GREEN moves piece G2 from 25 to 28
Yellow rolls 5
DEBUG: Piece Yellow3 at pos 11, die_roll 5, dist_from_home is 39, dist_from_start is 16, home_entry at 50
YELLOW moves piece Y3 from 11 to 16
Blue rolls 3
DEBUG: Piece Blue1 at pos 34, die_roll 3, dist_from_home is 31, dist_from_start is 24, home_entry at 11
BLUE moves piece B1 from 34 to 37
Red rolls 2
DEBUG: Piece Red1 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
DEBUG: Piece Red2 at pos 0, die_roll 2, dist_from_home is 27, dist_from_start is 28, home_entry at 24
RED moves piece R1 from 26 to 28 and gets a bonus roll!
Green rolls 4
DEBUG: Piece Green4 at pos 44, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 37
GREEN moves piece G4 from 44 to 48
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 16, die_roll 2, dist_from_home is 37, dist_from_start is 18, home_entry at 50
YELLOW moves piece Y3 from 16 to 18
Blue rolls 4
DEBUG: Piece Blue1 at pos 37, die_roll 4, dist_from_home is 27, dist_from_start is 28, home_entry at 11
BLUE moves piece B1 from 37 to 41
Red rolls 5
DEBUG: Piece Red1 at pos 28, die_roll 5, dist_from_home is 48, dist_from_start is 7, home_entry at 24
DEBUG: Piece Red2 at pos 0, die_roll 5, dist_from_home is 24, dist_from_start is 31, home_entry at 24
RED moves piece R2 from 0 to 5
Green rolls 4
DEBUG: Piece Green4 at pos 48, die_roll 4, dist_from_home is 42, dist_from_start is 13, home_entry at 37
GREEN moves piece G4 from 48 to 0
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 18, die_roll 2, dist_from_home is 35, dist_from_start is 20, home_entry at 50
YELLOW moves piece Y3 from 18 to 20
Blue rolls 2
DEBUG: Piece Blue1 at pos 41, die_roll 2, dist_from_home is 25, dist_from_start is 30, home_entry at 11
BLUE moves piece B1 from 41 to 43
Red rolls 1
DEBUG: Piece Red1 at pos 28, die_roll 1, dist_from_home is 52, dist_from_start is 3, home_entry at 24
DEBUG: Piece Red2 at pos 5, die_roll 1, dist_from_home is 23, dist_from_start is 32, home_entry at 24
RED moves piece R2 from 5 to 6
Green rolls 1
DEBUG: Piece Green4 at pos 0, die_roll 1, dist_from_home is 41, dist_from_start is 14, home_entry at 37
GREEN moves piece G4 from 0 to 1
Yellow rolls 1
DEBUG: Piece Yellow3 at pos 20, die_roll 1, dist_from_home is 34, dist_from_start is 21, home_entry at 50
YELLOW moves piece Y3 from 20 to 21
Blue rolls 5
DEBUG: Piece Blue1 at pos 43, die_roll 5, dist_from_home is 20, dist_from_start is 35, home_entry at 11
BLUE moves piece B1 from 43 to 48
Red rolls 4
DEBUG: Piece Red1 at pos 28, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Piece Red2 at pos 6, die_roll 4, dist_from_home is 19, dist_from_start is 36, home_entry at 24
RED moves piece R2 from 6 to 10
Green rolls 4
DEBUG: Piece Green4 at pos 1, die_roll 4, dist_from_home is 37, dist_from_start is 18, home_entry at 37
GREEN moves piece G4 from 1 to 5
Yellow rolls 5
DEBUG: Piece Yellow3 at pos 21, die_roll 5, dist_from_home is 29, dist_from_start is 26, home_entry at 50
YELLOW moves piece Y3 from 21 to 26
Blue rolls 5
DEBUG: Piece Blue1 at pos 48, die_roll 5, dist_from_home is 15, dist_from_start is 40, home_entry at 11
BLUE moves piece B1 from 48 to 1
Red rolls 6
DEBUG: Piece Red1 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
DEBUG: Piece Red2 at pos 10, die_roll 6, dist_from_home is 13, dist_from_start is 42, home_entry at 24
RED moves piece R3 from 0 to 26 and gets a bonus roll!
Green rolls 4
DEBUG: Piece Green4 at pos 5, die_roll 4, dist_from_home is 33, dist_from_start is 22, home_entry at 37
GREEN moves piece G4 from 5 to 9
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 1, die_roll 2, dist_from_home is 13, dist_from_start is 42, home_entry at 11
BLUE moves piece B1 from 1 to 3
Red rolls 2
DEBUG: Piece Red1 at pos 28, die_roll 2, dist_from_home is 51, dist_from_start is 4, home_entry at 24
DEBUG: Piece Red2 at pos 10, die_roll 2, dist_from_home is 17, dist_from_start is 38, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R2 from 10 to 12
Green rolls 5
DEBUG: Piece Green4 at pos 9, die_roll 5, dist_from_home is 28, dist_from_start is 27, home_entry at 37
GREEN moves piece G4 from 9 to 14
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 4
DEBUG: Piece Blue1 at pos 3, die_roll 4, dist_from_home is 9, dist_from_start is 46, home_entry at 11
BLUE moves piece B1 from 3 to 7
Red rolls 3
DEBUG: Piece Red1 at pos 28, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 24
DEBUG: Piece Red2 at pos 12, die_roll 3, dist_from_home is 14, dist_from_start is 41, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 24
RED moves piece R2 from 12 to 15
Green rolls 5
DEBUG: Piece Green4 at pos 14, die_roll 5, dist_from_home is 23, dist_from_start is 32, home_entry at 37
GREEN moves piece G4 from 14 to 19
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 1
DEBUG: Piece Blue1 at pos 7, die_roll 1, dist_from_home is 8, dist_from_start is 47, home_entry at 11
BLUE moves piece B1 from 7 to 8
Red rolls 5
DEBUG: Piece Red1 at pos 28, die_roll 5, dist_from_home is 48, dist_from_start is 7, home_entry at 24
DEBUG: Piece Red2 at pos 15, die_roll 5, dist_from_home is 9, dist_from_start is 46, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R2 from 15 to 20
Green rolls 3
DEBUG: Piece Green4 at pos 19, die_roll 3, dist_from_home is 20, dist_from_start is 35, home_entry at 37
GREEN moves piece G4 from 19 to 22
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 8, die_roll 2, dist_from_home is 6, dist_from_start is 49, home_entry at 11
BLUE moves piece B1 from 8 to 10
Red rolls 1
DEBUG: Piece Red1 at pos 28, die_roll 1, dist_from_home is 52, dist_from_start is 3, home_entry at 24
DEBUG: Piece Red2 at pos 20, die_roll 1, dist_from_home is 8, dist_from_start is 47, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R2 from 20 to 21
Green rolls 5
DEBUG: Piece Green4 at pos 22, die_roll 5, dist_from_home is 15, dist_from_start is 40, home_entry at 37
GREEN moves piece G4 from 22 to 27
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B2 from 0 to 13
Red rolls 4
DEBUG: Piece Red1 at pos 28, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Piece Red2 at pos 21, die_roll 4, dist_from_home is 4, dist_from_start is 51, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R2 from 21 to 25
Green rolls 3
DEBUG: Piece Green4 at pos 27, die_roll 3, dist_from_home is 12, dist_from_start is 43, home_entry at 37
GREEN moves piece G4 from 27 to 30
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 10, die_roll 2, dist_from_home is 4, dist_from_start is 51, home_entry at 11
BLUE moves piece B1 from 10 to 12
Red rolls 6
DEBUG: Piece Red1 at pos 28, die_roll 6, dist_from_home is 47, dist_from_start is 8, home_entry at 24
DEBUG: Piece Red2 at pos 25, die_roll 6, dist_from_home is -2, dist_from_start is 57, home_entry at 24
DEBUG: Crosses home entry at step 4, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Piece Red3 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
DEBUG: Applying move to home path, home_index = 1
RED moves piece R2 from 25 to 1
Green rolls 6
DEBUG: Piece Green4 at pos 30, die_roll 6, dist_from_home is 6, dist_from_start is 49, home_entry at 37
GREEN moves piece G2 from 0 to 39
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 3
DEBUG: Piece Blue2 at pos 13, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 11
BLUE moves piece B2 from 13 to 16
Red rolls 2
DEBUG: Piece Red1 at pos 28, die_roll 2, dist_from_home is 51, dist_from_start is 4, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R1 from 28 to 30 and gets a bonus roll!
Green rolls 3
DEBUG: Piece Green2 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 39 to 42
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 12, die_roll 2, dist_from_home is 2, dist_from_start is 53, home_entry at 11
BLUE moves piece B1 from 12 to 14
Red rolls 1
DEBUG: Piece Red1 at pos 30, die_roll 1, dist_from_home is 50, dist_from_start is 5, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R1 from 30 to 31
Green rolls 3
DEBUG: Piece Green2 at pos 42, die_roll 3, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G2 from 42 to 45
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 3
DEBUG: Piece Blue2 at pos 16, die_roll 3, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B2 from 16 to 19
Red rolls 2
DEBUG: Piece Red1 at pos 31, die_roll 2, dist_from_home is 48, dist_from_start is 7, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R1 from 31 to 33
Green rolls 4
DEBUG: Piece Green2 at pos 45, die_roll 4, dist_from_home is 45, dist_from_start is 10, home_entry at 37
GREEN moves piece G2 from 45 to 49
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 1
DEBUG: Piece Blue1 at pos 14, die_roll 1, dist_from_home is 1, dist_from_start is 54, home_entry at 11
BLUE moves piece B1 from 14 to 15
Red rolls 1
DEBUG: Piece Red1 at pos 33, die_roll 1, dist_from_home is 47, dist_from_start is 8, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R1 from 33 to 34
Green rolls 4
DEBUG: Piece Green2 at pos 49, die_roll 4, dist_from_home is 41, dist_from_start is 14, home_entry at 37
GREEN moves piece G2 from 49 to 1
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 4
DEBUG: Piece Blue2 at pos 19, die_roll 4, dist_from_home is 45, dist_from_start is 10, home_entry at 11
BLUE moves piece B2 from 19 to 23
Red rolls 2
DEBUG: Piece Red1 at pos 34, die_roll 2, dist_from_home is 45, dist_from_start is 10, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R1 from 34 to 36
Green rolls 3
DEBUG: Piece Green2 at pos 1, die_roll 3, dist_from_home is 38, dist_from_start is 17, home_entry at 37
GREEN moves piece G2 from 1 to 4
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 2
DEBUG: Piece Blue1 at pos 15, die_roll 2, dist_from_home is -1, dist_from_start is 56, home_entry at 11
DEBUG: Crosses home entry at step 1, steps_into_home = 1
DEBUG: Entering home path at index 1
DEBUG: Applying move to home path, home_index = 0
BLUE moves piece B1 from 15 to 0
Red rolls 2
DEBUG: Piece Red1 at pos 36, die_roll 2, dist_from_home is 43, dist_from_start is 12, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R1 from 36 to 38
Green rolls 6
DEBUG: Piece Green2 at pos 4, die_roll 6, dist_from_home is 32, dist_from_start is 23, home_entry at 37
GREEN moves piece G4 from 0 to 39
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 4
DEBUG: Piece Blue2 at pos 23, die_roll 4, dist_from_home is 41, dist_from_start is 14, home_entry at 11
BLUE moves piece B2 from 23 to 27
Red rolls 3
DEBUG: Piece Red1 at pos 38, die_roll 3, dist_from_home is 40, dist_from_start is 15, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 24
RED moves piece R1 from 38 to 41
Green rolls 3
DEBUG: Piece Green2 at pos 4, die_roll 3, dist_from_home is 35, dist_from_start is 20, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 4 to 7
Yellow rolls 2
YELLOW cannot move this turn.
Blue rolls 4
DEBUG: Piece Blue2 at pos 27, die_roll 4, dist_from_home is 37, dist_from_start is 18, home_entry at 11
BLUE moves piece B2 from 27 to 31
Red rolls 5
DEBUG: Piece Red1 at pos 41, die_roll 5, dist_from_home is 35, dist_from_start is 20, home_entry at 24
DEBUG: Piece Red3 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R3 from 26 to 31 and gets a bonus roll!
Green rolls 1
DEBUG: Piece Green2 at pos 7, die_roll 1, dist_from_home is 34, dist_from_start is 21, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 7 to 8
Yellow rolls 2
YELLOW cannot move this turn.
Blue rolls 1
BLUE cannot move this turn.
Red rolls 3
DEBUG: Piece Red1 at pos 41, die_roll 3, dist_from_home is 37, dist_from_start is 18, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R1 from 41 to 44
Green rolls 4
DEBUG: Piece Green2 at pos 8, die_roll 4, dist_from_home is 30, dist_from_start is 25, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 8 to 12
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 3
BLUE cannot move this turn.
Red rolls 5
DEBUG: Piece Red1 at pos 44, die_roll 5, dist_from_home is 32, dist_from_start is 23, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 24
RED moves piece R1 from 44 to 49
Green rolls 3
DEBUG: Piece Green2 at pos 12, die_roll 3, dist_from_home is 27, dist_from_start is 28, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 12 to 15
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 4
BLUE cannot move this turn.
Red rolls 3
DEBUG: Piece Red1 at pos 49, die_roll 3, dist_from_home is 29, dist_from_start is 26, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 3, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R1 from 49 to 0
Green rolls 2
DEBUG: Piece Green2 at pos 15, die_roll 2, dist_from_home is 25, dist_from_start is 30, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G2 from 15 to 17
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B4 from 0 to 13
Red rolls 1
DEBUG: Piece Red1 at pos 0, die_roll 1, dist_from_home is 28, dist_from_start is 27, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R1 from 0 to 1
Green rolls 4
DEBUG: Piece Green2 at pos 17, die_roll 4, dist_from_home is 21, dist_from_start is 34, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 17 to 21
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Reaching final home position
DEBUG: Piece Blue1 reaches final home
BLUE moves piece B1 from 0 to 5
Red rolls 6
DEBUG: Piece Red1 at pos 1, die_roll 6, dist_from_home is 22, dist_from_start is 33, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R1 from 1 to 7
Green rolls 4
DEBUG: Piece Green2 at pos 21, die_roll 4, dist_from_home is 17, dist_from_start is 38, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 21 to 25
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 1
DEBUG: Piece Blue4 at pos 13, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 11
BLUE moves piece B4 from 13 to 14
Red rolls 6
DEBUG: Piece Red1 at pos 7, die_roll 6, dist_from_home is 16, dist_from_start is 39, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R1 from 7 to 13
Green rolls 3
DEBUG: Piece Green2 at pos 25, die_roll 3, dist_from_home is 14, dist_from_start is 41, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 25 to 28
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue4 at pos 14, die_roll 5, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B4 from 14 to 19
Red rolls 6
DEBUG: Piece Red1 at pos 13, die_roll 6, dist_from_home is 10, dist_from_start is 45, home_entry at 24
DEBUG: Piece Red3 at pos 31, die_roll 6, dist_from_home is 44, dist_from_start is 11, home_entry at 24
RED moves piece R1 from 13 to 19 and gets a bonus roll!
Green rolls 3
DEBUG: Piece Green2 at pos 28, die_roll 3, dist_from_home is 11, dist_from_start is 44, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 37
GREEN moves piece G2 from 28 to 31 and gets a bonus roll!
Yellow rolls 2
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B2 from 0 to 13
Red rolls 1
DEBUG: Piece Red1 at pos 19, die_roll 1, dist_from_home is 9, dist_from_start is 46, home_entry at 24
RED moves piece R1 from 19 to 20
Green rolls 2
DEBUG: Piece Green2 at pos 31, die_roll 2, dist_from_home is 9, dist_from_start is 46, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G2 from 31 to 33
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 1
DEBUG: Piece Blue2 at pos 13, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 11
BLUE moves piece B2 from 13 to 14
Red rolls 4
DEBUG: Piece Red1 at pos 20, die_roll 4, dist_from_home is 5, dist_from_start is 50, home_entry at 24
DEBUG: Reaching final home position
DEBUG: Piece Red2 reaches final home
RED moves piece R2 from 1 to 5
Green rolls 2
DEBUG: Piece Green2 at pos 33, die_roll 2, dist_from_home is 7, dist_from_start is 48, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G2 from 33 to 35
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue2 at pos 14, die_roll 5, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B2 from 14 to 19
Red rolls 1
DEBUG: Piece Red1 at pos 20, die_roll 1, dist_from_home is 8, dist_from_start is 47, home_entry at 24
RED moves piece R1 from 20 to 21
Green rolls 4
DEBUG: Piece Green2 at pos 35, die_roll 4, dist_from_home is 3, dist_from_start is 52, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G2 from 35 to 39
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue2 at pos 19, die_roll 5, dist_from_home is 44, dist_from_start is 11, home_entry at 11
BLUE moves piece B2 from 19 to 24
Red rolls 1
DEBUG: Piece Red1 at pos 21, die_roll 1, dist_from_home is 7, dist_from_start is 48, home_entry at 24
RED moves piece R1 from 21 to 22
Green rolls 1
DEBUG: Piece Green2 at pos 39, die_roll 1, dist_from_home is 2, dist_from_start is 53, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G2 from 39 to 40
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 5
DEBUG: Piece Blue2 at pos 24, die_roll 5, dist_from_home is 39, dist_from_start is 16, home_entry at 11
BLUE moves piece B2 from 24 to 29
Red rolls 3
DEBUG: Piece Red1 at pos 22, die_roll 3, dist_from_home is 4, dist_from_start is 51, home_entry at 24
RED moves piece R1 from 22 to 25
Green rolls 1
DEBUG: Piece Green2 at pos 40, die_roll 1, dist_from_home is 1, dist_from_start is 54, home_entry at 37
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G4 from 39 to 40
Yellow rolls 4
YELLOW cannot move this turn.
Blue rolls 3
DEBUG: Piece Blue2 at pos 29, die_roll 3, dist_from_home is 36, dist_from_start is 19, home_entry at 11
BLUE moves piece B2 from 29 to 32
Red rolls 4
DEBUG: Piece Red1 at pos 25, die_roll 4, dist_from_home is 0, dist_from_start is 55, home_entry at 24
DEBUG: Crosses home entry at step 4, steps_into_home = 0
RED moves piece R1 from 25 to 29
Green rolls 5
DEBUG: Piece Green2 at pos 40, die_roll 5, dist_from_home is -3, dist_from_start is 58, home_entry at 37
DEBUG: Crosses home entry at step 2, steps_into_home = 3
DEBUG: Entering home path at index 3
DEBUG: Piece Green4 at pos 40, die_roll 5, dist_from_home is 49, dist_from_start is 6, home_entry at 37
DEBUG: Applying move to home path, home_index = 2
GREEN moves piece G2 from 40 to 2
Yellow rolls 5
YELLOW cannot move this turn.
Blue rolls 6
BLUE moves piece B4 from 0 to 13
Red rolls 2
DEBUG: Piece Red1 at pos 29, die_roll 2, dist_from_home is -2, dist_from_start is 57, home_entry at 24
DEBUG: Crosses home entry at step 0, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Applying move to home path, home_index = 1
RED moves piece R1 from 29 to 1
Green rolls 1
DEBUG: Piece Green4 at pos 40, die_roll 1, dist_from_home is 53, dist_from_start is 2, home_entry at 37
GREEN moves piece G4 from 40 to 41
Yellow rolls 1
YELLOW cannot move this turn.
Blue rolls 6
DEBUG: Piece Blue2 at pos 32, die_roll 6, dist_from_home is 30, dist_from_start is 25, home_entry at 11
BLUE moves piece B2 from 32 to 38
Red rolls 5
RED cannot move this turn.
Green rolls 3
DEBUG: Reaching final home position
DEBUG: Piece Green4 at pos 41, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 37
DEBUG: Piece Green2 reaches final home
GREEN moves piece G2 from 2 to 5
Yellow rolls 6
YELLOW moves piece Y1 from 0 to 0
Blue rolls 1
DEBUG: Piece Blue4 at pos 13, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 11
BLUE moves piece B4 from 13 to 14
Red rolls 2
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green4 at pos 41, die_roll 3, dist_from_home is 50, dist_from_start is 5, home_entry at 37
GREEN moves piece G4 from 41 to 44
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y1 from 0 to 2
Blue rolls 3
DEBUG: Piece Blue2 at pos 38, die_roll 3, dist_from_home is 27, dist_from_start is 28, home_entry at 11
BLUE moves piece B2 from 38 to 41
Red rolls 5
RED cannot move this turn.
Green rolls 1
DEBUG: Piece Green4 at pos 44, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 37
GREEN moves piece G4 from 44 to 45
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 2, die_roll 1, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 2 to 3
Blue rolls 6
DEBUG: Piece Blue4 at pos 14, die_roll 6, dist_from_home is 48, dist_from_start is 7, home_entry at 11
BLUE moves piece B4 from 14 to 20
Red rolls 5
RED cannot move this turn.
Green rolls 1
DEBUG: Piece Green4 at pos 45, die_roll 1, dist_from_home is 48, dist_from_start is 7, home_entry at 37
GREEN moves piece G4 from 45 to 46
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 3, die_roll 6, dist_from_home is 46, dist_from_start is 9, home_entry at 50
YELLOW moves piece Y3 from 0 to 0
Blue rolls 4
DEBUG: Piece Blue2 at pos 41, die_roll 4, dist_from_home is 23, dist_from_start is 32, home_entry at 11
BLUE moves piece B2 from 41 to 45
Red rolls 3
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green4 at pos 46, die_roll 3, dist_from_home is 45, dist_from_start is 10, home_entry at 37
GREEN moves piece G4 from 46 to 49
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 3, die_roll 2, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y1 from 3 to 5
Blue rolls 1
DEBUG: Piece Blue4 at pos 20, die_roll 1, dist_from_home is 47, dist_from_start is 8, home_entry at 11
BLUE moves piece B4 from 20 to 21
Red rolls 2
RED cannot move this turn.
Green rolls 3
DEBUG: Piece Green4 at pos 49, die_roll 3, dist_from_home is 42, dist_from_start is 13, home_entry at 37
GREEN moves piece G4 from 49 to 0 and gets a bonus roll!
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 5, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 50
YELLOW moves piece Y1 from 5 to 10
Blue rolls 3
DEBUG: Piece Blue2 at pos 45, die_roll 3, dist_from_home is 20, dist_from_start is 35, home_entry at 11
BLUE moves piece B2 from 45 to 48
Red rolls 6
RED moves piece R3 from 0 to 26
Green rolls 2
DEBUG: Piece Green4 at pos 0, die_roll 2, dist_from_home is 40, dist_from_start is 15, home_entry at 37
GREEN moves piece G4 from 0 to 2
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 10, die_roll 1, dist_from_home is 44, dist_from_start is 11, home_entry at 50
YELLOW moves piece Y1 from 10 to 11
Blue rolls 1
DEBUG: Piece Blue4 at pos 21, die_roll 1, dist_from_home is 46, dist_from_start is 9, home_entry at 11
BLUE moves piece B4 from 21 to 22
Red rolls 1
DEBUG: Piece Red3 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R3 from 26 to 27
Green rolls 2
DEBUG: Piece Green4 at pos 2, die_roll 2, dist_from_home is 38, dist_from_start is 17, home_entry at 37
GREEN moves piece G4 from 2 to 4
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 11, die_roll 3, dist_from_home is 41, dist_from_start is 14, home_entry at 50
YELLOW moves piece Y1 from 11 to 14
Blue rolls 6
DEBUG: Piece Blue2 at pos 48, die_roll 6, dist_from_home is 14, dist_from_start is 41, home_entry at 11
BLUE moves piece B2 from 48 to 2
Red rolls 1
DEBUG: Piece Red3 at pos 27, die_roll 1, dist_from_home is 53, dist_from_start is 2, home_entry at 24
RED moves piece R3 from 27 to 28
Green rolls 5
DEBUG: Piece Green4 at pos 4, die_roll 5, dist_from_home is 33, dist_from_start is 22, home_entry at 37
GREEN moves piece G4 from 4 to 9
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 14, die_roll 1, dist_from_home is 40, dist_from_start is 15, home_entry at 50
YELLOW moves piece Y1 from 14 to 15
Blue rolls 6
DEBUG: Piece Blue4 at pos 22, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 11
BLUE moves piece B4 from 22 to 28 and gets a bonus roll!
Red rolls 2
RED cannot move this turn.
Green rolls 4
DEBUG: Piece Green4 at pos 9, die_roll 4, dist_from_home is 29, dist_from_start is 26, home_entry at 37
GREEN moves piece G4 from 9 to 13
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 15, die_roll 3, dist_from_home is 37, dist_from_start is 18, home_entry at 50
YELLOW moves piece Y1 from 15 to 18
Blue rolls 1
DEBUG: Piece Blue2 at pos 2, die_roll 1, dist_from_home is 13, dist_from_start is 42, home_entry at 11
BLUE moves piece B2 from 2 to 3
Red rolls 6
RED moves piece R3 from 0 to 26
Green rolls 2
DEBUG: Piece Green4 at pos 13, die_roll 2, dist_from_home is 27, dist_from_start is 28, home_entry at 37
GREEN moves piece G4 from 13 to 15
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 18, die_roll 3, dist_from_home is 34, dist_from_start is 21, home_entry at 50
YELLOW moves piece Y1 from 18 to 21
Blue rolls 1
DEBUG: Piece Blue4 at pos 28, die_roll 1, dist_from_home is 39, dist_from_start is 16, home_entry at 11
BLUE moves piece B4 from 28 to 29
Red rolls 1
DEBUG: Piece Red3 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R3 from 26 to 27
Green rolls 1
DEBUG: Piece Green4 at pos 15, die_roll 1, dist_from_home is 26, dist_from_start is 29, home_entry at 37
GREEN moves piece G4 from 15 to 16
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 21, die_roll 2, dist_from_home is 32, dist_from_start is 23, home_entry at 50
YELLOW moves piece Y1 from 21 to 23
Blue rolls 4
DEBUG: Piece Blue2 at pos 3, die_roll 4, dist_from_home is 9, dist_from_start is 46, home_entry at 11
BLUE moves piece B2 from 3 to 7
Red rolls 6
DEBUG: Piece Red3 at pos 27, die_roll 6, dist_from_home is 48, dist_from_start is 7, home_entry at 24
RED moves piece R4 from 0 to 26
Green rolls 4
DEBUG: Piece Green4 at pos 16, die_roll 4, dist_from_home is 22, dist_from_start is 33, home_entry at 37
GREEN moves piece G4 from 16 to 20
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 23, die_roll 3, dist_from_home is 29, dist_from_start is 26, home_entry at 50
YELLOW moves piece Y1 from 23 to 26 and gets a bonus roll!
Blue rolls 1
DEBUG: Piece Blue4 at pos 29, die_roll 1, dist_from_home is 38, dist_from_start is 17, home_entry at 11
BLUE moves piece B4 from 29 to 30
Red rolls 5
DEBUG: Piece Red3 at pos 27, die_roll 5, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R3 from 27 to 32
Green rolls 1
DEBUG: Piece Green4 at pos 20, die_roll 1, dist_from_home is 21, dist_from_start is 34, home_entry at 37
GREEN moves piece G4 from 20 to 21
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 26, die_roll 2, dist_from_home is 27, dist_from_start is 28, home_entry at 50
YELLOW moves piece Y1 from 26 to 28
Blue rolls 6
DEBUG: Piece Blue2 at pos 7, die_roll 6, dist_from_home is 3, dist_from_start is 52, home_entry at 11
BLUE moves piece B2 from 7 to 13
Red rolls 2
DEBUG: Piece Red3 at pos 32, die_roll 2, dist_from_home is 47, dist_from_start is 8, home_entry at 24
RED moves piece R3 from 32 to 34
Green rolls 6
DEBUG: Piece Green4 at pos 21, die_roll 6, dist_from_home is 15, dist_from_start is 40, home_entry at 37
GREEN moves piece G4 from 21 to 27
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 28, die_roll 2, dist_from_home is 25, dist_from_start is 30, home_entry at 50
YELLOW moves piece Y1 from 28 to 30 and gets a bonus roll!
Blue rolls 5
DEBUG: Piece Blue2 at pos 13, die_roll 5, dist_from_home is -2, dist_from_start is 57, home_entry at 11
DEBUG: Crosses home entry at step 3, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Applying move to home path, home_index = 1
BLUE moves piece B2 from 13 to 1
Red rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Red3 at pos 34, die_roll 4, dist_from_home is 43, dist_from_start is 12, home_entry at 24
DEBUG: Piece Red1 reaches final home
RED moves piece R1 from 1 to 5
Green rolls 6
DEBUG: Piece Green4 at pos 27, die_roll 6, dist_from_home is 9, dist_from_start is 46, home_entry at 37
GREEN moves piece G4 from 27 to 33
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 30, die_roll 3, dist_from_home is 22, dist_from_start is 33, home_entry at 50
YELLOW moves piece Y1 from 30 to 33 and gets a bonus roll!
Blue rolls 2
BLUE cannot move this turn.
Red rolls 1
DEBUG: Piece Red3 at pos 34, die_roll 1, dist_from_home is 46, dist_from_start is 9, home_entry at 24
RED moves piece R3 from 34 to 35
Green rolls 1
GREEN cannot move this turn.
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 33, die_roll 3, dist_from_home is 19, dist_from_start is 36, home_entry at 50
YELLOW moves piece Y1 from 33 to 36
Blue rolls 6
BLUE moves piece B4 from 0 to 13
Red rolls 6
DEBUG: Piece Red3 at pos 35, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 24
RED moves piece R4 from 0 to 26
Green rolls 3
GREEN cannot move this turn.
Yellow rolls 1
DEBUG: Piece Yellow1 at pos 36, die_roll 1, dist_from_home is 18, dist_from_start is 37, home_entry at 50
YELLOW moves piece Y1 from 36 to 37
Blue rolls 3
DEBUG: Piece Blue4 at pos 13, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 11
BLUE moves piece B4 from 13 to 16
Red rolls 4
DEBUG: Piece Red3 at pos 35, die_roll 4, dist_from_home is 42, dist_from_start is 13, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R3 from 35 to 39
Green rolls 4
GREEN cannot move this turn.
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 37, die_roll 6, dist_from_home is 12, dist_from_start is 43, home_entry at 50
YELLOW moves piece Y3 from 0 to 0
Blue rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Blue2 reaches final home
BLUE moves piece B2 from 1 to 5
Red rolls 1
DEBUG: Piece Red3 at pos 39, die_roll 1, dist_from_home is 41, dist_from_start is 14, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R3 from 39 to 40
Green rolls 1
GREEN cannot move this turn.
Yellow rolls 2
DEBUG: Piece Yellow1 at pos 37, die_roll 2, dist_from_home is 16, dist_from_start is 39, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y1 from 37 to 39
Blue rolls 1
DEBUG: Piece Blue4 at pos 16, die_roll 1, dist_from_home is 51, dist_from_start is 4, home_entry at 11
BLUE moves piece B4 from 16 to 17
Red rolls 4
DEBUG: Piece Red3 at pos 40, die_roll 4, dist_from_home is 37, dist_from_start is 18, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R3 from 40 to 44
Green rolls 6
GREEN moves piece G4 from 0 to 39 and gets a bonus roll!
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 0, die_roll 2, dist_from_home is 53, dist_from_start is 2, home_entry at 50
YELLOW moves piece Y3 from 0 to 2
Blue rolls 3
DEBUG: Piece Blue4 at pos 17, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 11
BLUE moves piece B4 from 17 to 20
Red rolls 5
DEBUG: Piece Red3 at pos 44, die_roll 5, dist_from_home is 32, dist_from_start is 23, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R3 from 44 to 49
Green rolls 1
DEBUG: Piece Green4 at pos 39, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 37
GREEN moves piece G4 from 39 to 40
Yellow rolls 4
DEBUG: Piece Yellow3 at pos 2, die_roll 4, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y3 from 2 to 6
Blue rolls 1
DEBUG: Piece Blue4 at pos 20, die_roll 1, dist_from_home is 47, dist_from_start is 8, home_entry at 11
BLUE moves piece B4 from 20 to 21
Red rolls 1
DEBUG: Piece Red3 at pos 49, die_roll 1, dist_from_home is 31, dist_from_start is 24, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 1, dist_from_home is 54, dist_from_start is 1, home_entry at 24
RED moves piece R3 from 49 to 50
Green rolls 3
DEBUG: Piece Green4 at pos 40, die_roll 3, dist_from_home is 51, dist_from_start is 4, home_entry at 37
GREEN moves piece G4 from 40 to 43
Yellow rolls 4
DEBUG: Piece Yellow3 at pos 6, die_roll 4, dist_from_home is 45, dist_from_start is 10, home_entry at 50
YELLOW moves piece Y3 from 6 to 10
Blue rolls 1
DEBUG: Piece Blue4 at pos 21, die_roll 1, dist_from_home is 46, dist_from_start is 9, home_entry at 11
BLUE moves piece B4 from 21 to 22
Red rolls 6
DEBUG: Piece Red3 at pos 50, die_roll 6, dist_from_home is 25, dist_from_start is 30, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R3 from 50 to 4
Green rolls 3
DEBUG: Piece Green4 at pos 43, die_roll 3, dist_from_home is 48, dist_from_start is 7, home_entry at 37
GREEN moves piece G4 from 43 to 46
Yellow rolls 1
DEBUG: Piece Yellow3 at pos 10, die_roll 1, dist_from_home is 44, dist_from_start is 11, home_entry at 50
YELLOW moves piece Y3 from 10 to 11
Blue rolls 1
DEBUG: Piece Blue4 at pos 22, die_roll 1, dist_from_home is 45, dist_from_start is 10, home_entry at 11
BLUE moves piece B4 from 22 to 23
Red rolls 6
DEBUG: Piece Red3 at pos 4, die_roll 6, dist_from_home is 19, dist_from_start is 36, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 24
RED moves piece R3 from 4 to 10
Green rolls 4
DEBUG: Piece Green4 at pos 46, die_roll 4, dist_from_home is 44, dist_from_start is 11, home_entry at 37
GREEN moves piece G4 from 46 to 50
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 11, die_roll 2, dist_from_home is 42, dist_from_start is 13, home_entry at 50
YELLOW moves piece Y3 from 11 to 13
Blue rolls 1
DEBUG: Piece Blue4 at pos 23, die_roll 1, dist_from_home is 44, dist_from_start is 11, home_entry at 11
BLUE moves piece B4 from 23 to 24
Red rolls 5
DEBUG: Piece Red3 at pos 10, die_roll 5, dist_from_home is 14, dist_from_start is 41, home_entry at 24
DEBUG: Piece Red4 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R3 from 10 to 15
Green rolls 6
DEBUG: Piece Green4 at pos 50, die_roll 6, dist_from_home is 38, dist_from_start is 17, home_entry at 37
GREEN moves piece G4 from 50 to 4
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 13, die_roll 2, dist_from_home is 40, dist_from_start is 15, home_entry at 50
YELLOW moves piece Y3 from 13 to 15 and gets a bonus roll!
Blue rolls 6
DEBUG: Piece Blue4 at pos 24, die_roll 6, dist_from_home is 38, dist_from_start is 17, home_entry at 11
BLUE moves piece B4 from 24 to 30
Red rolls 5
DEBUG: Piece Red4 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
RED moves piece R4 from 26 to 31
Green rolls 6
DEBUG: Piece Green4 at pos 4, die_roll 6, dist_from_home is 32, dist_from_start is 23, home_entry at 37
GREEN moves piece G4 from 4 to 10
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 15, die_roll 2, dist_from_home is 38, dist_from_start is 17, home_entry at 50
YELLOW moves piece Y3 from 15 to 17
Blue rolls 4
DEBUG: Piece Blue4 at pos 30, die_roll 4, dist_from_home is 34, dist_from_start is 21, home_entry at 11
BLUE moves piece B4 from 30 to 34
Red rolls 4
DEBUG: Piece Red4 at pos 31, die_roll 4, dist_from_home is 46, dist_from_start is 9, home_entry at 24
RED moves piece R4 from 31 to 35
Green rolls 3
DEBUG: Piece Green4 at pos 10, die_roll 3, dist_from_home is 29, dist_from_start is 26, home_entry at 37
GREEN moves piece G4 from 10 to 13
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 17, die_roll 2, dist_from_home is 36, dist_from_start is 19, home_entry at 50
YELLOW moves piece Y3 from 17 to 19
Blue rolls 2
DEBUG: Piece Blue4 at pos 34, die_roll 2, dist_from_home is 32, dist_from_start is 23, home_entry at 11
BLUE moves piece B4 from 34 to 36
Red rolls 6
DEBUG: Piece Red4 at pos 35, die_roll 6, dist_from_home is 40, dist_from_start is 15, home_entry at 24
RED moves piece R3 from 0 to 26
Green rolls 2
DEBUG: Piece Green4 at pos 13, die_roll 2, dist_from_home is 27, dist_from_start is 28, home_entry at 37
GREEN moves piece G4 from 13 to 15
Yellow rolls 2
DEBUG: Piece Yellow3 at pos 19, die_roll 2, dist_from_home is 34, dist_from_start is 21, home_entry at 50
YELLOW moves piece Y3 from 19 to 21
Blue rolls 4
DEBUG: Piece Blue4 at pos 36, die_roll 4, dist_from_home is 28, dist_from_start is 27, home_entry at 11
BLUE moves piece B4 from 36 to 40
Red rolls 5
DEBUG: Piece Red3 at pos 26, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 24
DEBUG: Piece Red4 at pos 35, die_roll 5, dist_from_home is 41, dist_from_start is 14, home_entry at 24
RED moves piece R4 from 35 to 40 and gets a bonus roll!
Green rolls 4
DEBUG: Piece Green4 at pos 15, die_roll 4, dist_from_home is 23, dist_from_start is 32, home_entry at 37
GREEN moves piece G4 from 15 to 19
Yellow rolls 1
DEBUG: Piece Yellow3 at pos 21, die_roll 1, dist_from_home is 33, dist_from_start is 22, home_entry at 50
YELLOW moves piece Y3 from 21 to 22
Blue rolls 3
BLUE cannot move this turn.
Red rolls 4
DEBUG: Piece Red3 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
DEBUG: Piece Red4 at pos 40, die_roll 4, dist_from_home is 37, dist_from_start is 18, home_entry at 24
RED moves piece R4 from 40 to 44
Green rolls 2
DEBUG: Piece Green4 at pos 19, die_roll 2, dist_from_home is 21, dist_from_start is 34, home_entry at 37
GREEN moves piece G4 from 19 to 21
Yellow rolls 4
DEBUG: Piece Yellow3 at pos 22, die_roll 4, dist_from_home is 29, dist_from_start is 26, home_entry at 50
YELLOW moves piece Y3 from 22 to 26 and gets a bonus roll!
Blue rolls 3
BLUE cannot move this turn.
Red rolls 6
DEBUG: Piece Red4 at pos 44, die_roll 6, dist_from_home is 31, dist_from_start is 24, home_entry at 24
RED moves piece R3 from 0 to 26 and gets a bonus roll!
Green rolls 5
DEBUG: Piece Green4 at pos 21, die_roll 5, dist_from_home is 16, dist_from_start is 39, home_entry at 37
GREEN moves piece G4 from 21 to 26 and gets a bonus roll!
Yellow rolls 3
YELLOW cannot move this turn.
Blue rolls 4
BLUE cannot move this turn.
Red rolls 5
DEBUG: Piece Red4 at pos 44, die_roll 5, dist_from_home is 32, dist_from_start is 23, home_entry at 24
RED moves piece R4 from 44 to 49
Green rolls 3
DEBUG: Piece Green4 at pos 26, die_roll 3, dist_from_home is 13, dist_from_start is 42, home_entry at 37
GREEN moves piece G4 from 26 to 29
Yellow rolls 6
YELLOW moves piece Y1 from 0 to 0
Blue rolls 1
BLUE cannot move this turn.
Red rolls 1
DEBUG: Piece Red4 at pos 49, die_roll 1, dist_from_home is 31, dist_from_start is 24, home_entry at 24
RED moves piece R4 from 49 to 50
Green rolls 6
DEBUG: Piece Green4 at pos 29, die_roll 6, dist_from_home is 7, dist_from_start is 48, home_entry at 37
GREEN moves piece G4 from 29 to 35
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 0 to 5
Blue rolls 1
BLUE cannot move this turn.
Red rolls 5
DEBUG: Piece Red4 at pos 50, die_roll 5, dist_from_home is 26, dist_from_start is 29, home_entry at 24
RED moves piece R4 from 50 to 3
Green rolls 2
DEBUG: Piece Green4 at pos 35, die_roll 2, dist_from_home is 5, dist_from_start is 50, home_entry at 37
GREEN moves piece G4 from 35 to 37
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 5, die_roll 5, dist_from_home is 45, dist_from_start is 10, home_entry at 50
YELLOW moves piece Y1 from 5 to 10
Blue rolls 1
BLUE cannot move this turn.
Red rolls 1
DEBUG: Piece Red4 at pos 3, die_roll 1, dist_from_home is 25, dist_from_start is 30, home_entry at 24
RED moves piece R4 from 3 to 4
Green rolls 3
DEBUG: Piece Green4 at pos 37, die_roll 3, dist_from_home is 2, dist_from_start is 53, home_entry at 37
GREEN moves piece G4 from 37 to 40
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 10, die_roll 6, dist_from_home is 39, dist_from_start is 16, home_entry at 50
YELLOW moves piece Y3 from 0 to 0
Blue rolls 5
BLUE cannot move this turn.
Red rolls 3
DEBUG: Piece Red4 at pos 4, die_roll 3, dist_from_home is 22, dist_from_start is 33, home_entry at 24
RED moves piece R4 from 4 to 7
Green rolls 4
DEBUG: Piece Green4 at pos 40, die_roll 4, dist_from_home is -2, dist_from_start is 57, home_entry at 37
DEBUG: Crosses home entry at step 2, steps_into_home = 2
DEBUG: Entering home path at index 2
DEBUG: Applying move to home path, home_index = 1
GREEN moves piece G4 from 40 to 1
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 10, die_roll 5, dist_from_home is 40, dist_from_start is 15, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 10 to 15
Blue rolls 5
BLUE cannot move this turn.
Red rolls 3
DEBUG: Piece Red4 at pos 7, die_roll 3, dist_from_home is 19, dist_from_start is 36, home_entry at 24
RED moves piece R4 from 7 to 10
Green rolls 6
GREEN cannot move this turn.
Yellow rolls 6
DEBUG: Piece Yellow1 at pos 15, die_roll 6, dist_from_home is 34, dist_from_start is 21, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 6, dist_from_home is 49, dist_from_start is 6, home_entry at 50
YELLOW moves piece Y4 from 0 to 0
Blue rolls 6
BLUE moves piece B4 from 0 to 13
Red rolls 3
DEBUG: Piece Red4 at pos 10, die_roll 3, dist_from_home is 16, dist_from_start is 39, home_entry at 24
RED moves piece R4 from 10 to 13 and gets a bonus roll!
Green rolls 6
GREEN cannot move this turn.
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 15, die_roll 5, dist_from_home is 35, dist_from_start is 20, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 15 to 20
Blue rolls 6
BLUE moves piece B4 from 0 to 13 and gets a bonus roll!
Red rolls 6
RED moves piece R3 from 0 to 26
Green rolls 6
GREEN cannot move this turn.
Yellow rolls 3
DEBUG: Piece Yellow1 at pos 20, die_roll 3, dist_from_home is 32, dist_from_start is 23, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 3, dist_from_home is 52, dist_from_start is 3, home_entry at 50
YELLOW moves piece Y1 from 20 to 23
Blue rolls 5
DEBUG: Piece Blue4 at pos 13, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 11
BLUE moves piece B4 from 13 to 18
Red rolls 4
DEBUG: Piece Red3 at pos 26, die_roll 4, dist_from_home is 51, dist_from_start is 4, home_entry at 24
RED moves piece R3 from 26 to 30
Green rolls 1
GREEN cannot move this turn.
Yellow rolls 5
DEBUG: Piece Yellow1 at pos 23, die_roll 5, dist_from_home is 27, dist_from_start is 28, home_entry at 50
DEBUG: Piece Yellow3 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
DEBUG: Piece Yellow4 at pos 0, die_roll 5, dist_from_home is 50, dist_from_start is 5, home_entry at 50
YELLOW moves piece Y1 from 23 to 28
Blue rolls 1
DEBUG: Piece Blue4 at pos 18, die_roll 1, dist_from_home is 49, dist_from_start is 6, home_entry at 11
BLUE moves piece B4 from 18 to 19
Red rolls 6
DEBUG: Piece Red3 at pos 30, die_roll 6, dist_from_home is 45, dist_from_start is 10, home_entry at 24
RED moves piece R4 from 0 to 26
Green rolls 4
DEBUG: Reaching final home position
DEBUG: Piece Green4 reaches final home
GREEN moves piece G4 from 1 to 5
Player Green won
