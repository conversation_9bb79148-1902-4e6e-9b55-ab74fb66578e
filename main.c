#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "types.h"
#include "logic.h"

int main() {
    int game_over = 0;
    // initialize piece
    srand(time(NULL));
    initialize_pieces();
    initialize_board();
    initialize_players();

    // printf("Piece %s%d is in base: %d\n", 
    //     get_color_name(all_pieces[GREEN][0].color),
    //     all_pieces[GREEN][0].id,
    //     all_pieces[GREEN][0].is_in_base);

    // first player and order selection
    Color first_player = RED;
    int highest_roll = 0;

    for (int i = 0; i < NUM_PLAYERS; i++) {
        int roll = roll_die();
        printf("%s player rolls %d\n", get_color_name(i), roll);

        if (roll > highest_roll) {
            highest_roll = roll;
            first_player = i;
        }
    }

    printf("%s player has the highest roll and will begin the game.\n", get_color_name(first_player));
    Color color_order[] = {first_player,(first_player+1)%NUM_PLAYERS,(first_player+2)%NUM_PLAYERS,(first_player+3)%NUM_PLAYERS};
    for (int i = 0; i <NUM_PLAYERS; i++) {
       players[i].order = (first_player+i)%NUM_PLAYERS;
    }

    printf("The order of a single round is %s, %s, %s, %s\n", get_color_name(first_player), get_color_name((first_player+1)%NUM_PLAYERS), get_color_name((first_player+2)%NUM_PLAYERS), get_color_name((first_player+3)%NUM_PLAYERS));
    Color current = first_player;
    // for (int i = 0; i < NUM_PLAYERS; i++) {
    //     current = color_order[i];
    //     for (int j = 0; j < PIECES_PER_PLAYER; j++) {
    //         printf("%i ",START_POSITION[i]); 
    //     }
    //     printf("\n");
    // }
    while (!game_over) {
        for (int i=0; i < NUM_PLAYERS; i++){
            int bonus = 0;
            current = color_order[i];
            // current = BLUE;
            int roll = roll_die();
            printf("%s rolls %d\n", get_color_name(current), roll);
    
            switch (current)
            {
            case RED:
                bonus = play_red_turn(roll);
                break;
            case BLUE:
                bonus = play_blue_turn(roll);
                break;
            case GREEN:
                bonus = play_green_turn(roll);
                break;
            case YELLOW:
                bonus = play_yellow_turn(roll);
                break;
            
            default:
                break;
            }
            
            if (check_win(current)) {
                printf("Player %s won\n", get_color_name(current));
                game_over = 1;
                break;
            }
                
            
            if (!bonus) current = (current + 1) % NUM_PLAYERS;
        }


    }
    return 0;
}

